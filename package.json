{"name": "deepxi", "version": "3.3.0", "description": "玉柴智眸行业信息系统", "author": "北京滴普科技", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint:fix": "eslint . --fix", "lint": "eslint ."}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "^11.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "autoprefixer": "^10.4.20", "axios": "0.24.0", "crypto-js": "^4.0.0", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "echarts": "^5.6.0", "element-plus": "2.9.0", "file-saver": "2.0.5", "fuse.js": "6.4.6", "js-base64": "^3.7.7", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "lodash": "^4.17.21", "nprogress": "0.2.0", "postcss-pxtorem": "^6.1.0", "vue": "3.5.13", "vue-cropper": "1.0.2", "vue-echarts": "^7.0.3", "vue-router": "4.0.12", "vue-word-highlighter": "^1.2.5", "vue3-water-marker": "^0.0.5", "vuex": "4.0.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@vitejs/plugin-vue": "4.3.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/compiler-sfc": "3.2.22", "eslint": "^9.17.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "globals": "^15.14.0", "mockjs": "^1.1.0", "prettier": "^3.4.2", "sass": "1.45.0", "unplugin-auto-import": "0.5.3", "vite": "4.5.2", "vite-plugin-compression": "0.3.6", "vite-plugin-mock": "^3.0.2", "vite-plugin-svg-icons": "1.0.5", "vite-plugin-vue-devtools": "^7.7.5", "vite-plugin-vue-setup-extend": "0.1.0", "vue-eslint-parser": "^9.4.3"}}