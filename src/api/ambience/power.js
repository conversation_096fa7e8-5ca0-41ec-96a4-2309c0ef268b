import request from '@/utils/request'
import { getYAxisName, changeDataType } from '@/views/ambience/components/commonConfigData'
import { ElMessage } from 'element-plus'
import { splitDate } from './macro'
import { changeYearMonth, sortMonth, getYearMonthBu, getYearMonthBu2 } from './market'
import {
  combineOther,
  getOtherNumber,
  dataConvertForLine,
  dataConvertForTopN,
  dataConvertForPercentTopN,
  dataConvertForPercent
} from '../../utils/dataconvert'

const KEYNAUE = ['yuchai', 'weichai', 'kangsm', 'jiefangdl', 'hangye']

let XS = {
  'yuchai': '玉柴',
  'weichai': '潍柴',
  'kangsm': '康明斯',
  // 'jiefangdl': '解放动力',
  // 'hangye': '行业'
  'quanchai': '全柴',
  xichai: '锡柴',
  yunnei: '云内',
  'selectedEngine': '洋马'
}

const HY = {
  jiubaotian: '久保田',
  kangmingsi: '康明斯',
  other: '其他',
  sanling: '三菱',
  weichai: '潍柴',
  wushiling: '五十铃',
  yangma: '洋马',
  yuchai: '玉柴',
  total: '行业'
}

const HY1 = {
  yuchai: '玉柴',
  weichai: '潍柴',
  kangsm: '康明斯',
  hangye: '行业',
  jiefangdl: '解放动力'
}

const sortYearWeek = yearMonths => {
  // console.log(yearMonths,'000000')
  const yms = yearMonths?.sort((a, b) => {
    let a1 = a?.replace(new RegExp('-', 'g'), '')
    let b1 = b?.replace(new RegExp('-', 'g'), '')
    if (a && a.indexOf('NULL') > -1) {
      a1 = a1?.replace(new RegExp('NULL', 'g'), '')
      b1 = b1?.replace(new RegExp('NULL', 'g'), '')
    }
    if (b && b.indexOf('NULL') > -1) {
      a1 = a1?.replace(new RegExp('NULL', 'g'), '')
      b1 = b1?.replace(new RegExp('NULL', 'g'), '')
    }
    if (a && a.indexOf('汇总') > -1) {
      a1 = a1?.replace(new RegExp('汇总', 'g'), '')
      a1 = `${a1}13`
    }

    if (b1 && b1.indexOf('汇总') > -1) {
      b1 = b1?.replace(new RegExp('汇总', 'g'), '')
      b1 = `${b1}13`
    }

    let _a = a1 === NaN ? 0 : Number(a1)
    let _b = b1 === NaN ? 0 : Number(b1)
    return a1 * 1 - b1 * 1
  })
  return yms
}

const sortYearWeekName = (yearMonths, fex = '周') => {
  let mark = ''
  const new_yms = yearMonths.map(item => {
    let [year = '', week = ''] = item?.split('-')
    if (week === '汇总') {
      return `${year}年${week}`
    }

    let ym = `${1 * week}${fex}`
    if (mark !== year) {
      mark = year
      ym = `${year}年${1 * week}${fex}`
    }

    return ym
  })
  return new_yms
}

const changeSegmentedListData = (segmentedList, query = {}) => {
  const keys = {
    '1': 'month',
    '6': 'week'
  }
  let valuesObj = {}
  const ymsY = new Set()
  const { dataSource = '' } = query

  const key1 = keys[dataSource]
  const yms = segmentedList?.map(objItem => {
    const _objItem1 = Object.values(objItem)
      ?.filter(item => item.indexOf('com') === -1)
      ?.map(item2 => ({ ...item2[0] }))

    _objItem1?.forEach(itemObj => {
      let { [key1]: week = '', year = '' } = itemObj || {}
      week = dataSource === '1' && week.length === 1 ? `0${week}` : week
      const weekYear = `${year}-${week}`
      if (year || week) {
        ymsY.add(weekYear)
      }
      Object.keys(itemObj)?.forEach(i => {
        let keys = Object.keys(XS)
        if (keys.includes(i)) {
          const obj2 = valuesObj[i] || []

          valuesObj = {
            ...valuesObj,
            [i]: [
              ...obj2,
              {
                yearmonthweek: itemObj.yearmonthweek,
                value: itemObj[i],
                name: itemObj.enginefacturer,
                weekYear,
                yearMonth: dataSource === '6' ? `${year}年-${itemObj?.month}月` : ''
                // year:itemObj?.year,
                // month:
              }
            ]
          }
        } else {
        }
      })
    })

    return _objItem1
  })
  let pfg = dataSource === '1' ? '月' : '周'
  const _yms2 = sortYearWeek([...ymsY]).filter(i => i?.indexOf('NULL') === -1)
  const _ymsData = sortYearWeekName(_yms2, pfg)
  const series = Object.keys(valuesObj)?.map(key => {
    let saleslistTotal = []
    const saleslist = _yms2.map((y, index) => {
      let value = ''
      let yearMonth = ''
      const list = valuesObj[key]?.filter(({ weekYear: m = '' }) => m === y)
      if (list && list.length > 0) {
        value = `${list[0]?.value}`
        yearMonth = list[0]?.yearMonth || ''
      }
      saleslistTotal.push({
        value,
        name: _ymsData[index],
        _yearMonth: yearMonth
      })
      return value
    })
    //TODO  未来多选 可以使用join
    let name = query.engineFactory ? query.engineFactory : XS[key]

    return {
      name: name,
      data: saleslistTotal, //saleslist,
      type: 'line',
      symbolSize: 1
    }
  })

  let indexA = 0
  let rotate = 0
  if (dataSource === '1' && _yms2.length > 24) {
    // rotate = 30
  }
  if (dataSource === '6' && _yms2.length > 24) {
    // rotate = 30
  }

  const chart1 = {
    height: '23%',
    title: {
      text: '细分市场销售趋势图'
    },
    yAxis: {
      name: getYAxisName('台')
    },
    xAxis: {
      // data: sortYearWeekName(_yms2),
      axisLabel: {
        rotate,
        interval: (index, value) => {
          if (dataSource === '1') return true
          if (index === 0) return true
          if (value?.indexOf('年') > -1) {
            indexA = index
            return true
          } else if (index - indexA <= 1 && index - indexA > 0) {
            return false
          } else if (_yms2.length <= 48) {
            return true
          } else {
            return index % 4 === 0
          }
        }
      }
      // name:getYAxisName('周'),
    },
    series: series //series
  }

  return chart1
}

const changecomList = data => {
  // if (!data || data.length === 0) return []
  let yms1 = new Set()

  let valuesObj = {}
  data?.map(objItem => {
    const _objItem = Object.values(objItem)
      ?.filter(item => item.indexOf('com') === -1)
      ?.forEach(itemlist => {
        // 统计不足100%处理，剩余的值都归到其他
        // if(itemlist[0].enginefacturer){
        //   itemlist = getOtherNumber(itemlist, 'enginefacturer', 'proportion')
        // }else{
        //   itemlist = getOtherNumber(itemlist, 'engine_manufacturer', 'proportion')
        // }

        itemlist?.forEach(item => {
          let { yearmonth, enginefacturer, year, engine_manufacturer = '' } = item
          enginefacturer = enginefacturer ? enginefacturer : engine_manufacturer
          yearmonth = yearmonth ? getYearMonthBu2(yearmonth, '-') : `${year}-汇总`

          const obj2 = valuesObj[enginefacturer] || []
          yms1.add(yearmonth)

          valuesObj = {
            ...valuesObj,
            [enginefacturer]: [
              ...obj2,
              {
                ...item,
                yearmonth,
                name: enginefacturer,
                enginefacturer
              }
            ]
          }
        })
      })
    return _objItem
  })

  let newYms = [...yms1] //.filter((item)=>(item.indexOf('汇总') === -1))
  let _newYms = sortYearWeek(newYms)
  let names = sortYearWeekName(_newYms, '月')

  let newObj = Object.keys(valuesObj)

  let seriesList = []

  // 循环data 判断是否有sales_volume 对象 如果有就将sales_volume的值赋值到sales中
  data?.forEach(objItem => {
    const _objItem = Object.values(objItem)
      ?.filter(item => item.indexOf('com') === -1)
      ?.forEach(itemlist => {
        itemlist?.forEach(item => {
          if (item.hasOwnProperty('sales_volume')) {
            item.sales = item.sales_volume
          }
        })
      })
  })

  let series = newObj.map(key => {
    let saleslistTotal = []
    let saleslistTotal2 = []
    _newYms.map((y, index) => {
      let _sales = ''
      let _sales2 = ''
      const list = valuesObj[key]?.filter(({ yearmonth: m = '' }) => {
        return m === y
      })
      // 只筛选包含“汇总”的数据
      
      if (list && list.length > 0) {
        const { sales = '', sales_volume = '', proportion = '' } = list[0]
        _sales = `${sales || sales_volume}`
        _sales2 = proportion
      }

      saleslistTotal.push({
        // name: names[index],
        value: _sales,
        yearmonth: _newYms[index],
        name: y
      })
      saleslistTotal2.push({
        name: names[index],
        value: _sales2,
        yearmonth: _newYms[index]
      })

      return _sales
    })

    seriesList.push({
      name: key,
      data: saleslistTotal2, //saleslist,
      stack: 'total',
      barWidth: '10%',
      type: 'bar',
      barGap: '40%'
    })

    return {
      name: key,
      data: saleslistTotal, //saleslist,
      stack: 'total',
      barWidth: '10%',
      type: 'bar',
      barGap: '40%'
      // barCategoryGap: "80%"
    }
  })

  series = dataConvertForTopN(series, 11)

  // seriesList = dataConvertForTopN(seriesList, 11)
  seriesList.forEach(ele => {
    ele.data.forEach(el => {
      if (isNaN(el.value)) el.value = ''
    })
  })

  series = series?.map(item => {
    return {
      ...item,
      stack: 'total',
      barWidth: '10%',
      type: 'bar',
      barGap: '40%'
    }
  })

  const chart = {
    title: {
      text: `商用车月度发动机销量走势`
    },
    xAxis: {
      axisLine: {
        show: true
      }
    },
    yAxis: {
      name: getYAxisName('万台')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: series
  }
  const chart3 = {
    title: {
      text: `商用车月度发动机结构走势`
    },
    xAxis: {
      axisLine: {
        show: true
      }
    },
    yAxis: {
      name: getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: seriesList
  }
      

  return { chart, chart3 }
}

const comListTotal = (data, list) => {
  let valuesObj = {}
  const yms = data?.map(item => {
    const keyValy = Object.entries(item)
    const keyValy_ = keyValy[0][1]
    let month = keyValy[0][0]

    keyValy_?.forEach(itemObj => {
      const { engine_manufacturer = '' } = itemObj
      let enginefacturer = engine_manufacturer
      if (enginefacturer === '0') {
        enginefacturer = keyValy[0][0]
      }
      const obj2 = valuesObj[enginefacturer] || []
      valuesObj = {
        ...valuesObj,
        [enginefacturer]: [
          ...obj2,
          {
            ...itemObj,
            yearmonth: month,
            name: enginefacturer
          }
        ]
      }
    })

    if (month && month?.indexOf('汇总')) {
      month = month?.replace(new RegExp('汇总', 'g'), '') || ''
    }
    return month
  })

  const _yms = yms.filter(i => !(i.indexOf('-') > -1))
  const _yms1 = yms.filter(i => i.indexOf('-') > -1)
  const sortY = sortMonth([..._yms1])
  const allYMS = [..._yms1, ..._yms]
  let _yms2 = changeYearMonth(allYMS)
  _yms2[_yms2.length - 1] = `${_yms2[_yms2.length - 1]}汇总`

  const series = Object.keys(valuesObj)?.map(key => {
    // const { name } = item
    let saleslistTotal = []
    const saleslist = allYMS.map((y, index) => {
      let value = 0
      const list = valuesObj[key]?.filter(
        ({ yearmonth: m = '', year: e = '' }) => m === y || e == y
      )
      if (list && list.length > 0) {
        value = `${list[0]?.proportion}` || ''
      }
      saleslistTotal.push({
        name: _yms2[index],
        value
      })
      return value
    })

    return {
      name: key,
      data: saleslistTotal, //saleslist,
      stack: 'total',
      barWidth: '40%',
      type: 'bar'
    }
  })

  const chart = {
    title: {
      text: '商用车月度发动机结构走势'
    },
    xAxis: {
      data: _yms2
    },
    yAxis: {
      name: getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: series
  }

  return chart
}

//竞争环境-细分动力
export const genMachineCptTrendList = async query => {
  const { date = undefined, dataType, ...otherQuery } = query
  const newDate = splitDate(date)
  const _dataType = changeDataType(dataType)
  const { dataSource = '', engineFactory } = query

  let obj = {
    engineFactory
  }
  if (dataSource === '9' && engineFactory) {
    let _engineFactory = engineFactory ? engineFactory.join(',') : ''
    obj.engineArr = engineFactory
    obj.engineFactory = ''
    obj.subMarket = query?.subMarket1
  }

  const params = {
    ...newDate,
    dataType: _dataType,
    //根据业务需求，year查询的是结束年份
    year: newDate?.endYear || '',
    ...otherQuery,
    ...obj,
    dataSource: query?.dataSource === '9' ? '9' : query?.dataSource
  }

  try {
    let res = await request({
      url: '/intelligence/marketEnv/segmentedPowerList',
      method: 'post',
      data: params
    })
    if (res.code === 200) {
      const { data = {} } = res
      // if(params?.data)
      let chart1 = []
      let chart2 = []
      let chart3 = []
      let chart4 = []
      let chart5 = []
      let _seriesList = []

      if (query?.dataSource === '1' || query?.dataSource === '6') {
        chart1 = changeSegmentedListData(data?.segmentedList || [], query)
        // chart1.series.forEach((item)=>{
        //   item.data.forEach((item2)=>{
        //     if(item2.value == '0'){
        //       item2.value = ""
        //     }
        //   })
        // })
        let { chart = {}, chart3: achart = [] } = changecomList(data?.comList || [])
        chart2 = chart
        chart3 = JSON.parse(JSON.stringify(achart)) //comListTotal(data?.comListTotal || [],_seriesList)
      }

      if (query?.dataSource === '2') {
        chart4 = changethreeYearListata(data?.threeYearList || [], query)
      }

      if (query?.dataSource === '9') {
        chart5 = changesegmentedList(data?.engineTrendList || [], query)
      }
      chart2 = convertChartUnit(chart2);
      return {
        chart1,
        chart2,
        chart3,
        chart4,
        chart5
      }
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

const changethreeYearListata = (segmentedList, query) => {
  if (!segmentedList || segmentedList.length === 0) return []
  let valuesObj = {}
  let yms1 = new Set()

  let _data = segmentedList?.map(item =>
    Object.values(item)?.filter(item => item?.indexOf('com') === -1)
  )
  _data?.forEach(items =>
    items?.forEach(items1 => {
      items1?.forEach(item2 => {
        const { yearmonth = '', ...otherItem } = item2
        let mon = yearmonth ? getYearMonthBu2(yearmonth, '-') : yearmonth
        yms1.add(mon)
        Object.keys(otherItem)
          ?.filter(item => item.indexOf('@') === -1)
          ?.forEach(i => {
            const obj2 = valuesObj[i] || []
            valuesObj = {
              ...valuesObj,
              [i]: [
                ...obj2,
                {
                  yearmonth: mon,
                  value: otherItem[i],
                  name: HY[i],
                  _yearmonth: mon
                }
              ]
            }
          })
      })
    })
  )

  const _yms2 = sortYearWeek([...yms1])
  const _sortYearWeekName = sortYearWeekName(_yms2, '月')

  const series = Object.keys(valuesObj)?.map(key => {
    let saleslistTotal = []
    const saleslist = _yms2.map((y, index) => {
      const list = valuesObj[key]?.filter(({ yearmonth: m = '' }) => m === y)
      let value = 0
      if (list && list.length > 0) {
        value = `${list[0]?.value}`
      }
      saleslistTotal.push({
        name: _sortYearWeekName[index],
        value
      })
      return value
    })

    return {
      name: HY[key],
      data: saleslistTotal, //saleslist,
      type: 'line',
      symbolSize: 1
    }
  })

  const { subMarket1 = '', subMarket2 = '' } = query

  const chart1 = {
    span: 24,
    title: {
      text: `近三年${subMarket1}${subMarket2}动力走势`
    },
    yAxis: {
      name: getYAxisName('台')
    },
    xAxis: {
      data: sortYearWeekName(_yms2, '月')
    },
    series: series
  }

  return chart1
}

const changesegmentedList = (data = [], params = {}) => {
  if (!data || data.length === 0) {
    return []
  }
  let valuesObj = {}
  let yms = new Set()

  let _data = data.map(item => Object.values(item)?.filter(item => item?.indexOf('com') === -1))
  _data?.forEach(items =>
    items.forEach(items1 => {
      items1?.forEach(item2 => {
        const valueitem = item2
        const { engine_manufacturer: enginefacturer, yearMonth } = valueitem
        const obj2 = valuesObj[enginefacturer] || []
        let mon = yearMonth ? getYearMonthBu2(yearMonth, '-') : yearMonth
        yms.add(mon)
        valuesObj = {
          ...valuesObj,
          [enginefacturer]: [
            ...obj2,
            {
              ...valueitem,
              yearMonth: mon,
              name: enginefacturer
            }
          ]
        }
      })
    })
  )

  const _yms2 = sortYearWeek([...yms])
  const _sortYearWeekName = sortYearWeekName(_yms2, '月')

  const series = Object.keys(valuesObj)?.map(key => {
    let saleslistTotal = []
    const saleslist = _yms2.map((y, index) => {
      const list = valuesObj[key]?.filter(({ yearMonth: m = '' }) => m === y)
      let value = 0
      if (list && list.length > 0) {
        value = `${list[0]?.sales}` || ''
      }
      saleslistTotal.push({
        name: _sortYearWeekName[index],
        yearmonth: y,
        _name: y,
        value
      })
      return value
    })
    return {
      name: key,
      data: saleslistTotal, //saleslist,
      type: 'line',
      symbolSize: 1
    }
  })

  const { date = '' } = params

  let _date1 = ''
  if (date && date.length) {
    date.forEach(item => {
      let [key1] = item.split('-')
      if (_date1) {
        _date1 += '-' + key1
      } else {
        _date1 = key1
      }
    })
  }

  const chart1 = {
    span: 24,
    title: {
      text: `${_date1}主要发动机厂家销量月度走势-友商数`
    },
    yAxis: {
      name: getYAxisName('台')
    },

    series: series
  }

  return chart1
}


/**
 * 自动转换图表数据和单位（直接修改配置）
 * @param {Object} chartConfig - 原始图表配置
 * @returns {Object} - 更新后的图表配置（可能修改了series.data和yAxis.name）
 */
function convertChartUnit(chartConfig) {
  // 深拷贝配置避免污染原数据
  const config = JSON.parse(JSON.stringify(chartConfig));
  // 检测是否需要转换
  const needConvert = config.series.some(series =>
      series.data.some(item => item.value >= 10000)
  );
  if (needConvert) {
    // 1. 转换所有value为万单位
    config.series.forEach(series => {
      series.data.forEach(item => {
        if (typeof item.value === 'number') {
          item.value = parseFloat((item.value / 10000).toFixed(2)); // 保留2位小数
        }
      });
    });
  }
  // 2. 更新yAxis单位显示
  config.yAxis = {
    ...config.yAxis,
    name: needConvert ? '单位：(万台)' : '单位：(台)',
  };
  return config; // 直接返回最终配置
}
