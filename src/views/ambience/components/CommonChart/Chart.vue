<template>

  
  <Bar
    :titleIcon="props.titleIcon"
    :series="newOptions.series"
    :height="defaultHeight[props?.heightKey] ? defaultHeight[props?.heightKey] : props?.height"
    :="{ ...active1optionslist }"    
    :title=" props.isPropsTitle ? props.title.text : active1optionslist.title"
  />

</template>

<script setup>
import * as echarts from 'echarts'
import { getAddTotal } from '../commonConfigData'
import { seriesSrice, getInitactiveOption, defaultHeight } from './config'
import { numberFormat } from '@/utils/format.js'
import Bar from '@/views/components/echarts/bar.vue'
import { color } from '@/views/components/echarts/config'

import { dictsResource } from '@/utils/common/dicts.js'

const props = defineProps({
  heightKey: '',
  color: {
    type: Array,
    require: false,
    default: color
  },
  height: {
    type: String,
    default: '30%'
  },
  // 左上标题
  title: {
    type: Object,
    require: false
  },
  isPropsTitle: {
    type: Boolean,
    default: false
  },
  titleIcon: {
    // 是否展示标题
    type: String,
    required: false,
    default: 'data1'
  },
  tooltip: {
    type: Object,
    require: false
  },
  // 图表解释
  legend: {
    type: Object,
    require: false
  },
  //图表额外配置覆盖
  otherOptions: {
    require: false,
    type: Object
  },
  xAxis: {
    require: false,
    type: Object
  },
  yAxis: {
    require: false,
    type: Object
  },
  series: {
    require: false,
    type: Object
  },
  // 外层样式
  chartStyle: {
    type: Object,
    default: {}
  },
  // X轴坐标倾斜度
  rotate: {
    type: Number,
    require: false
  },
  paddingStyle: {
    type: Object,
    default: null
  },
  // 是否显示柱状图总数
  barTotal: {
    type: Boolean,
    require: false,
    default: false
  },
  // 是否拼接标题
  comTitle: {
    type: Boolean,
    default: false
  },

  dataSource: {
    type: String
    // default:''
  },
  subMarket1Name: {
    type: String,
    default: ''
  },
  // 默认选择的图的标题，配置config
  selectedName: {
    type: String,
    default: ''
  }
})

// const props = computed(() => _props);
const active1options = getInitactiveOption(props)
// 初始化实例
let myChart = null
const chartRef = ref(null)
const newOptions = ref(active1options)
const active = ref('0')

const active1optionslist = computed(() => getInitactiveOption(props))
onMounted(() => {

  if (toRaw(active) === '0') {
    myChart = echarts.init(chartRef.value)
    commitMyChart()
    window.addEventListener('resize', function () {
      myChart.resize()
    })
  }
})

const commitMyChart = () => {
  const options = getInitOption()
  // 通过实例.setOption(options)
  myChart?.setOption(options)
}

// 构建options,配置对象
/** @type EChartsOption */
const getInitOption = () => {
  let title = props?.title || {}
  const rotate = props?.rotate
  const yAxis = props?.yAxis || {}
  const xAxis = props?.xAxis || {}
  const tooltip = props?.tooltip || {}
  const legend = props?.legend || {}
  let { series = [] } = props
  const otherOptions = props?.otherOptions || {}
  const { barTotal, selectedName = '' } = props
  let dataZoom = []

  let _rotate = rotate
  if (series && series.length) {
    // let series
    // // 把其他排后面
    series = series.sort(({ name = '' }) => {
      return name.indexOf('其他' || '其它')
    })
  }

  // 缩放
  if (series && series.length > 0) {
    const len = series?.map(({ data = [] }) => data?.length)
    if (len && Math.max(...len) > 12) {
      _rotate = rotate || 40
      dataZoom = [
        {
          type: 'inside'
        }
      ]
    }
    // 单条折线图显示数字

    if (series.length <= 2 && series[0]?.type === 'line') {
      let _series = [...series]
      series = _series.map(item => {
        return {
          ...item,
          label: {
            show: true
          }
        }
      })
    }
  }

  // 柱状图上显示数字
  if (barTotal && series && series.length) {
    tooltip.formatter = params => {
      
      return getAddTotal(params, false)
    }
    const _series = series?.filter(({ data = [] }) => data?.reduce((a, b) => a + b) > 0)
    if (_series && _series.length) {
      const { name = '' } = _series[0]
      const newSeries = series.filter(({ name: n }) => n !== name)
      const label = {
        show: true,
        position: 'top',
        width: 40,
        height: 40,
        formatter: params => {
          let total = 0
          series?.map(({ data = [] }) => {
            total += Number(data[params.dataIndex]) || 0
            return data[params.dataIndex]
          })
          // total = Number(total)
          return numberFormat(total)
        }
      }
      newSeries.push({ ..._series[0], label })
      series = newSeries
    }
  }

  // 对标题进行拼接
  if (title?.text) {
    let pre = ''
    if (props?.dataSource) {
      pre = props?.dataSource
        ? dictsResource?.filter(({ value }) => value === props?.dataSource)[0]?.label
        : ''
      pre = `-${pre}`
    }
    title = {
      ...title,
      text: `${props?.subMarket1Name || ''}${title.text}${pre}`
    }
  }

  // 指定展示的数据
  if (selectedName) {
    series = seriesSrice(selectedName, [...series])
  }

  const options = {
    color: [
      '#337ecc',
      '#5fceff',
      '#02a9f9',
      '#115e93',
      '#2970da',
      '#051c2c',
      '#67C23A',
      '#f3d19e',
      '#909399',
      '#73767a',
      '#606266',
      '#606266',
      '#000000'
    ],
    tooltip: {
      show: true,
      zlevel: 1000,
      trigger: 'axis',
      axisPointer: {
        z: 1000
      },
      formatter: params => {
        return getAddTotal(params, true, yAxis)
      },
      ...tooltip
    },
    grid: { left: 36, bottom: 66, top: 36 },
    title: {
      top: '20px',
      left: 'center',
      textStyle: {
        fontSize: 14
      },
      ...title
    },

    xAxis: {
      type: 'category',
      ...xAxis
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: false // 不显示x轴网格线
      },
      //   min: function (value) {
      //    return value.min;
      //  },
      ...yAxis
    },
    legend: {
      bottom: '0px',
      right: '40px',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 14
      },
      // selected:selectedLegend(selectedName,series),
      ...legend
    },
    series,
    dataZoom: dataZoom,
    ...otherOptions
  }
  return options
}
</script>
