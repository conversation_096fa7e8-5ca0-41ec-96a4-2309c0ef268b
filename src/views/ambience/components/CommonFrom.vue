<template>
  <el-form :inline="true" :model="formInline" class="demo-form-inline tabs-form">
    <el-row :gutter="16" style="margin-right: unset">
      <el-col :span="3">
        <el-form-item>
          <el-date-picker
            v-model="formInline.date"
            type="yearrange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY"
            :disabledDate="disabledFeatureDate"
            @clear="clearDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2" v-if="!props?.igoneShow.includes('pointerType')">
        <el-form-item prop="pointerType">
          <el-select v-model="formInline.pointerType" placeholder="指标类型" style="width: 100%">
            <el-option
              v-for="item in filterdictsPointerType()"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        v-model:form="formInline"
        :dicts="dataDict?.data"
        :props="showPropAll?.dictsResourceForm"
        :span="filterSpan()"
        :="{ ...propsManu }"
      />
      <el-col :span="3" v-if="showPropAll?.isTdataType">
        <el-form-item prop="dataType">
          <el-select
            multiple
            v-model="formInline.dataType"
            placeholder="数据扩展"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in dataClassification"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="1">
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
const store = useStore()
import { dictsResource } from '@/utils/common/dicts.js'
import DictsResource from '@/views/components/DictsResource.vue'
import { dataClassification } from '@/utils/common/dicts.js'
import { filterdictsPointerType, filterSpan } from './commonConfigData'
import useInnerData from '@/utils/hooks/innerData.js'

const emit = defineEmits(['change'])

const props = defineProps({
  iscike: {
    type: Boolean,
    required: false,
    default: false
  },
  params: {
    type: Object,
    required: true,
    default: () => ({
      startYear: '', // 起始年份
      endYear: '', // 截止年份
      pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
      dataSource: '', // 数据来源
      segment: '', // 板块
      subMarket1: '', // 细分市场1
      subMarket2: '', // 细分市场2
      manuFacturer: '', // 主机厂
      engineFactory: '', // 发动机厂
      fuelType: '', // 燃料
      weightMidLight: '',
      year: '' // 年份区间（仅前端操作用）
    })
  },
  dictsResourceValue: {
    type: Array,
    default: ['1', '6']
  },
  dictsResourceForm: {
    type: Array,
    default: [
      {
        name: '数据来源',
        key: 'dataSource'
      },
      {
        name: '板块',
        key: 'segment'
      },
      {
        name: '细分市场一',
        key: 'subMarket1'
      },
      // {
      //   name: '细分市场二',
      //   key: 'subMarket2',
      //   hide: true
      // }
    ]
  },
  showProp: {
    type: Array,
    default: []
  },
  igoneShow: {
    type: Array,
    default: []
  }
})

const showPropAll = reactive({
  showProp: props?.showProp,
  dictsResourceForm: props?.dictsResourceForm,
  isTdataType: false
})

const { hash = '' } = location
let fuelTypeA = 'B'
if (hash.indexOf('marketenergy') > -1) {
  fuelTypeA = 'A'
}
const propsManu = reactive({
  propsManuFacturer: {
    name: '主机厂',
    key: 'manuFacturer',
    show: showPropAll?.showProp.includes('manuFacturer')
  },
  propsEngineFactory: {
    name: '发动机厂',
    key: 'engineFactory',
    show: showPropAll?.showProp.includes('engineFactory')
  },
  propsFuelType: {
    name: '燃料',
    key: 'fuelType',
    show: showPropAll?.showProp.includes('fuelType'),
    type: fuelTypeA
  },
  propsWeightMidLight: {
    name: '重中轻',
    key: 'weightMidLight',
    show: showPropAll?.showProp.includes('weightMidLight'),
    disabled: false
  },
  propsBreed: { name: '品系', key: 'breed', show: showPropAll?.showProp.includes('breed') },
  // propsDataType: {
  //   name: '数据扩展',
  //   key: 'dataType',
  //   show: true
  // }
})

const dataDict = reactive({
  data: []
})

const getData = () => {
  propsManu.propsManuFacturer = { name: '主机厂', key: 'manuFacturer', show: false }
  propsManu.propsEngineFactory = { name: '发动机厂', key: 'engineFactory', show: false }
  propsManu.propsFuelType = { name: '燃料', key: 'fuelType', show: false }
  propsManu.propsBreed = { name: '品系', key: 'breed', show: false }
}

const dictsResourceValue = props?.dictsResourceValue
const dictsResourceList = dictsResource.filter(({ value }) => dictsResourceValue.includes(value))

const formInline = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 formInline 和 onSubmit
const { initDateRange, innerdate, disabledFeatureDate } = useInnerData(
  formInline,
  onSubmit,
  true,
  'date'
)
function clearDate() {
  formInline.date = ['', '']
  console.log('清空日期', formInline.date)
}
watch(
  () => formInline.pointerType,
  val => {
    innerdate()
  }
)
// 监听年份变化
watch(
  () => formInline.date,
  val => {
    // console.log('监听年份变化', val)
    innerdate()
  }
)
watch(
  () => formInline.breed,
  () => {
    if (formInline?.dataSource === '1' || formInline?.dataSource === '6') {
      let subMarket2Disabled = false
      if (formInline?.breed) {
        subMarket2Disabled = true
      }

      if (formInline?.subMarket2) {
        breedDisabled = true
      }

      let data = props?.dictsResourceForm
      data = data.filter(({ key = '' }) => key !== 'subMarket2')
      showPropAll.dictsResourceForm = [
        ...data,
        {
          name: '细分市场二',
          key: 'subMarket2',
          disabled: subMarket2Disabled
        }
      ]
    }
  }
)

watch(
  () => formInline.subMarket2,
  () => {
    if (formInline?.dataSource === '1' || formInline?.dataSource === '6') {
      let breedDisabled = false
      if (formInline?.breed) {
        subMarket2Disabled = true
      }

      if (formInline?.subMarket2) {
        breedDisabled = true
      }
      propsManu.propsBreed = { name: '品系', key: 'breed', show: true, disabled: breedDisabled }
    }
  }
)
const dataSourceObj = {
  '1': '上险数',
  '2': '装机数',
  '5': '海关数据',
  '6': '货运新增数',
  '7': '船电数',
  '9': '友商数'
}
watch(
  () => formInline.dataSource,
  () => {
    const dataSource = formInline?.dataSource
    const { hash = '' } = location
    // console.log('数据来源', dataSource)
    if (dataSource) {
      initDateRange(dataSourceObj[dataSource])
    }
    if (hash.indexOf('marketpower') > -1) {
      if (dataSource === '9') {
        propsManu.propsManuFacturer = { name: '主机厂', key: 'manuFacturer', show: false }
        // propsManu.propsEngineFactory={ name: '发动机厂', key: 'engineFactory', show: true }
        // propsManu.propsFuelType = { name: '燃料', key: 'fuelType', show: false }
        propsManu.propsBreed = { name: '品系', key: 'breed', show: false }
      } else {
        showPropAll.showProp = props?.showProp
        propsManu.propsManuFacturer = { name: '主机厂', key: 'manuFacturer', show: true }
        // propsManu.propsEngineFactory={ name: '发动机厂', key: 'engineFactory', show: true }
        // propsManu.propsFuelType = { name: '燃料', key: 'fuelType', show: true }
        propsManu.propsBreed = { name: '品系', key: 'breed', show: true }
        // formInline.engineFactory = ''
      }
    }

    if (formInline?.dataSource === '1') {
      showPropAll.isTdataType = true
      formInline.segment = '商用车'
    } else {
      showPropAll.isTdataType = false
      formInline.dataType = ''
    }

    if (dataSource === '6') {
      formInline.segment = '商用车'
      formInline.subMarket1 = '卡车'
    }

    // 需要将细分市场2 换成重中轻的，只有上险，货运，流向

    if (dataSource === '1' || dataSource === '6' || dataSource === '10') {
      showPropAll.dictsResourceForm[3].hide = true
      propsManu.propsWeightMidLight = {
        name: '重中轻',
        key: 'weightMidLight',
        show: showPropAll?.showProp.includes('weightMidLight'),
        disabled: false
      }
    } else {
      showPropAll.dictsResourceForm[3].hide = false
      propsManu.propsWeightMidLight = {
        name: '重中轻',
        key: 'weightMidLight',
        show: false,
        disabled: false
      }
    }
  }
)

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['上险数', '货运新增数', '友商数', '装机数', '船电数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    const { dictsResourceValue } = props
    let _dicts = dicts.filter(({ value = '' }) => dictsResourceValue.includes(value))
    //  console.log(_dicts,'0000')
    if (_dicts && _dicts.length === 1) {
      // showPropAll.dictsResourceForm = props?.dictsResourceForm;
      let data = props?.dictsResourceForm
      data = data.filter(({ key = '' }) => key !== 'dataSource')
      showPropAll.dictsResourceForm = [
        {
          name: '数据来源',
          key: 'dataSource',
          disabled: true
        },
        ...data
      ]
    }

    dataDict.data = _dicts
  }
}

initDateRange(dataSourceObj[formInline?.dataSource], true)
console.log('数据来源', dataSourceObj[formInline?.dataSource])
getDictsData()

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function onSubmit() {
  const data = toRaw(formInline)
  emit('change', data)
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  width: 100%;
}
</style>
