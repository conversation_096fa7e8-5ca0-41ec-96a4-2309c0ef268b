<template>
  <CommonTabs>
        <template #searchArea>  
    <!-- 搜索框 -->
    <SearchFormResource
      class="search_form_box"
      :params="searchForm"
      :linkageData="searchConfig.linkageData"
      @change="onSubmit"
    />
      <!-- 分类 -->
    <el-segmented
      ref="segmentedRef"
      v-model="searchConfig.subMarket"
      :options="searchConfig.dictSubMarket"
      @change="tabChange"
    >
      <template #default="scope">
        <div>{{ scope.item.label }}</div>
      </template>
    </el-segmented>
    </template>
  
    <!-- 内容 -->
    <!-- <div class="el-row-sclorl"> -->
    <el-row :gutter="20" type="flex" style="margin-left: 0; margin-right: 0">
      <el-col :xs="24" :sm="24" :md="8">
        <bar
          titleIcon="data1"
          v-loading="barDataLeft1.loading"
          :title="barDataLeft1.title"
          :series="barDataLeft1.data"
          y-axis-name=" "
          tooltipUnits=""
          :precision="0"
          show-total
          title-rotate
          :grid="{ left: 66, bottom: 36, right: 100, top: 46 }"
          :legend="{ orient: 'vertical', top: 'middle', right: 4 }"
          addTooltipTotalPercent
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <mixBarLine
          v-loading="barDataRight1.loading"
          :title="barDataRight1.title"
          y-axis-name=" "
          :series="barDataRight1.data"
          :grid="{ left: 66, bottom: 24, right: 125, top: 46 }"
          :legend="{ orient: 'vertical', top: 'middle', right: 4 }"
          :color="colors.arr"
          :show-total="false"
          :xAxis="{
            data:
              searchConfig.params.pointerType != '1' ? referData.monthSort : referData.quarterSort,
            nameTextStyle: { height: '220px' }
          }"
          height="270px"
          :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                singleColumn: true,
                shouldSort: false,
                showTotal: true,
                sortField: 'value'
              })
          }"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <bar
          titleIcon="data1"
          v-loading="barDataLeft2.loading"
          :title="barDataLeft2.title"
          :series="barDataLeft2.data"
          y-axis-name=" "
          tooltipUnits=""
          :precision="0"
          show-total
          title-rotate
          :grid="{ left: 66, bottom: 36, right: 100, top: 46 }"
          :legend="{ orient: 'vertical', top: 'middle', right: 4 }"
          addTooltipTotalPercent
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <mixBarLine
          v-loading="barDataRight2.loading"
          :title="barDataRight2.title"
          y-axis-name=" "
          :series="barDataRight2.data"
          :grid="{ left: 66, bottom: 24, right: 125, top: 46 }"
          :legend="{ orient: 'vertical', top: 'middle', right: 4 }"
          :color="colors.arr"
          :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                singleColumn: true,
                shouldSort: false,
                showTotal: true,
                sortField: 'value'
              })
          }"
          :show-total="false"
          height="270px"
        />
      </el-col>
    </el-row>
    <!-- </div> -->
  </CommonTabs>
</template>

<script setup lang="jsx">
import { onMounted, toRefs } from 'vue'
import mixBarLine from './components/mixBarLine.vue'
import SearchFormResource from './components/SearchFormResource.vue'
import bar from '@/views/components/echarts/bar.vue'
import Chart from '@/views/ambience/components/CommonChart/Chart'
// import CommonTabs from '@/views/ambience/components/CommonTabs'
import CommonTabs from '@/views/components/tabs/CommonTabs'

import {
  shipYearSaleTrend,
  shipMonthSaleTrend,
  shipManufacturerYearSaleTrend,
  shipManufacturerSlice
} from '@/api/ambience/electricity'
import { throttle } from '../../../../utils'
import calChartsData from '@/utils/hooks/calChartsData.js'
import { setYuchaiColor } from '@/utils/common/method.js'
import { TooltipFormatter } from '@/utils/common/method.js'
import Tpis from '@/views/components/tooltip/index.vue'
import { typePointerType } from '@/utils/common/dicts'
const store = useStore()

import { reactive } from 'vue'
import { TooltipComponent } from '../../../components/jsx/TooltipComponent'
const searchForm = reactive({
  year: (new Date().getFullYear() - 1).toString(), // 年份
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  month: '12', // 月
  // quarter: '', // 季度
  dataSource: '7', // 数据来源
  segment: '船电', // 板块
  subMarket: '' // 细分市场
})

const { referData } = calChartsData()
const colors = reactive({
  arr: ['#115E93', '#87AEC9', '#3A76FF']
})

const searchConfig = reactive({
  params: { ...searchForm },
  subMarket: '整体',
  linkageData: [], // 多级联动数据
  dictSubMarket: []
})
const barDataLeft1 = reactive({
  title: '船电总体市场年度销量趋势',
  loading: false,
  data: []
}) // 第1行左侧报表
const barDataRight1 = reactive({
  title: '船电总体市场月度销量趋势',
  loading: false,
  data: []
}) // 第1行右侧报表

const barDataLeft2 = reactive({
  title: '船电市场发动机销量趋势',
  loading: false,
  data: []
}) // 第2行左侧报表
const barDataRight2 = reactive({
  title: '船电市场发动机年度销量同比',
  loading: false,
  data: []
}) // 第2行右侧报表

const segmentedRef = ref(null)

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['船电数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    searchConfig.linkageData = dicts
    const cds = dicts.find(item => item.label === '船电数')
    const chuandian = cds.children?.find(item => item.label === '船电')
    searchConfig.dictSubMarket = [{ label: '整体', value: '整体' }]
    chuandian.children.forEach(element => {
      // 把自己放进去
      searchConfig.dictSubMarket.push({ label: element.label, value: element.value })
      // 是否有子项，有则丢进去
      if (element.children) {
        element.children.forEach(child => {
          searchConfig.dictSubMarket.push({
            label: child.label,
            value: child.value,
            parent: element.value
          })
        })
      }
    })
  }
}
getDictsData()

function getPercent() {
  const curWidth = segmentedRef.value.$el.offsetWidth
  const parentWidth = segmentedRef.value.$parent.$el.offsetWidth
  return parentWidth / curWidth
}
const length = ref(searchConfig.dictSubMarket.length)
const resizeDom = throttle(() => {
  if (!segmentedRef.value) return
  const percent = getPercent()
  length.value = Math.floor(searchConfig.dictSubMarket.length * percent.toFixed(2))
  nextTick(() => {
    const percent = getPercent()
    if (percent < 1) {
      length.value--
    }
  })
})
onMounted(() => {
  // getSalesSliceList(searchForm)
  // getSalesSliceList1(searchForm)
  resizeDom()
  if (window.ResizeObserver) {
    // 使用 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      // console.log('ResizeObserver: ')
      resizeDom()
    })

    // 开始监听容器大小变化
    resizeObserver?.observe(segmentedRef.value.$parent.$el)
  } else {
    window.addEventListener('resize', () => {
      // console.log('onresize: ')
      resizeDom()
    })
  }

  nextTick(() => {
    barDataLeft1.data = []
    barDataLeft2.data = []

    barDataRight1.data = []
    barDataRight2.data = []
  })
})

function tabChange(subMarket) {
  searchConfig.subMarket = subMarket
  barDataLeft1.title = `${subMarket === '整体' ? '船电' : subMarket}总体市场年度销量趋势`
  barDataRight1.title = `${subMarket === '整体' ? '船电' : subMarket}总体市场${typePointerType[searchConfig.params.pointerType]}销量趋势`
  // if (subMarket === '整体') {
  //   colors.arr = ['#DF8342', '#5071BE', '#44546A']
  // }
  // if (subMarket === '船机') {
  //   colors.arr = ['#5071BE', '#44546A']
  // }
  // if (subMarket === '单机') {
  //   colors.arr = ['#DF8342', '#44546A']
  // }
  getDataLetf1()
  getDataRight1()
  getDataLetf2()
  getDataRight2()
}
// 由搜索组件控制发起请求，包括首次请求
const onSubmit = params => {
  console.log('params查询参数',params)
  searchConfig.params = params
  getDataLetf1()
  getDataRight1()
  getDataLetf2()
  getDataRight2()
  barDataRight1.title = `${searchConfig.params.subMarket === '整体' || searchConfig.params.subMarket === ''  ? '船电' : searchConfig.params.subMarket.subMarket}总体市场${typePointerType[searchConfig.params.pointerType]}销量趋势`
  barDataRight2.title = `船电市场发动机${typePointerType[searchConfig.params.pointerType]}销量同比`
}

function apiSearchParam() {
  const param = { ...searchConfig.params, subMarket: searchConfig.subMarket }
  delete param.quarter
  const mk = searchConfig.dictSubMarket.find(item => item.value === param.subMarket)
  if (mk && mk.parent) {
    param.subMarket1 = mk.parent
    param.subMarket2 = param.subMarket
  } else {
    param.subMarket1 = param.subMarket === '整体' ? '' : param.subMarket
    param.subMarket2 = ''
  }
  delete param.subMarket

  return param
}

// 报表数据请求1
const getDataLetf1 = async () => {
  if (barDataLeft1.loading) return
  barDataLeft1.loading = true

  shipYearSaleTrend(apiSearchParam())
    .then(data => {
      // data?.forEach(bar => {
      //   if (bar.name === '单机') {
      //     bar.itemStyle = { color: '#df8342' }
      //   }
      //   if (bar.name === '船机') {
      //     bar.itemStyle = { color: '#5071be' }
      //   }
      // })
      barDataLeft1.data = data
    })
    .finally(() => {
      barDataLeft1.loading = false
    })
}

// 报表数据请求2
const getDataRight1 = async () => {
  if (barDataRight1.loading) return
  barDataRight1.loading = true

  shipMonthSaleTrend({
    ...apiSearchParam(),
    pointerType: searchConfig.params.pointerType == '2' ? '0' : searchConfig.params.pointerType
  })
    .then(data => {
      barDataRight1.data = data
    })
    .finally(() => {
      barDataRight1.loading = false
    })
}

// 报表数据请求3
const getDataLetf2 = async () => {
  if (barDataLeft2.loading) return
  barDataLeft2.loading = true

  shipManufacturerYearSaleTrend(apiSearchParam())
    .then(data => {
      data?.forEach(bar => {
        // if (bar.name === 'MTU') {
        //   bar.itemStyle = { color: '#7eab56' }
        // }
        // if (bar.name === '重康') {
        //   bar.itemStyle = { color: '#6b9ad0' }
        // }
        // if (bar.name === '上柴') {
        //   bar.itemStyle = { color: '#5071be' }
        // }
        // if (bar.name === '东康') {
        //   bar.itemStyle = { color: '#a5a5a5' }
        // }
        // if (bar.name === '潍柴') {
        //   bar.itemStyle = { color: '#df8342' }
        // }
        if (bar.name === '玉柴') {
          bar.itemStyle = { color: '#E72331' }
        }
      })
      barDataLeft2.data = data
    })
    .finally(() => {
      barDataLeft2.loading = false
    })
}

// 报表数据请求4
const getDataRight2 = async () => {
  if (barDataRight2.loading) return
  barDataRight2.loading = true

  shipManufacturerSlice(apiSearchParam())
    .then(data => {
      let d = setYuchaiColor(data)

      barDataRight2.data = d
    })
    .finally(() => {
      barDataRight2.loading = false
    })
}


</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';

.search_form_box {
  margin-bottom: 0px;
}
.el-row-sclorl {
  margin-top: 20px;
}
.el-row-sclorl_whlie {
  background: #fff;
  margin-top: 10px;
  border-radius: 10px;
  .is-guttered {
    margin-bottom: 0px;
  }
  .is-always-shadow {
    box-shadow: none;
    border-radius: 0px;
  }
}
</style>
<style>
.chartTips .tipItems.wrap {
  min-height: max-content;
  width: 1.1rem;
  padding: 0px;
}
</style>
