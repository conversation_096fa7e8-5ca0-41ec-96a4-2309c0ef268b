<template>
  <div style="overflow: hidden">
    <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
      <el-row :gutter="16">
        <el-col :span="4" :xs="8" :sm="8" :md="3">
          <el-form-item prop="year">
            <el-date-picker
              v-model="params.year"
              type="year"
              value-format="YYYY"
              format="YYYY"
              :disabled-date="disabledFeatureDate"
              placeholder="年份"
              :clearable="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="pointerType">
            <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
              <el-option
                v-for="item in dictsPointerType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :xs="8" :sm="8" :md="3">
          <el-form-item v-if="params.pointerType === '2'" prop="month">
            <el-select v-model="params.month" placeholder="月累" style="width: 100%">
              <el-option
                v-for="item in newDictsMonthTotal"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
            <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
              <el-option
                v-for="item in newDictsQuarter"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else prop="month">
            <el-select v-model="params.month" placeholder="月度" style="width: 100%">
              <el-option
                v-for="item in newDictsMonth"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <DictsResource
          :form="params"
          :dicts="data.linkageData"
          :props="[
            {
              name: '数据来源',
              key: 'dataSource',
              // hide: true,
              clearable: true
            },
            {
              name: '板块',
              key: 'segment',
              clearable: true,
              disabled: true
            },
            {
              name: '细分市场一',
              key: 'subMarket1'
            },
            {
              name: '细分市场二',
              key: 'subMarket2',
              hide: true,
              disabled: true
            }
          ]"
          :propsBreed="{ name: '品系', key: 'breed', show: false, disabled: false }"
          :propsWeightMidLight="{
            name: '重中轻',
            key: 'weightMidLight',
            show: true,
            disabled: false
          }"
          :propsFuelType="{ name: '燃料', key: 'fuelType', show: true, type: 'B' }"
          :propsDataType="{
            name: '数据扩展',
            key: 'dataType',
            show: true
          }"
          :xs="8"
          :sm="8"
          :md="3"
        />
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="province">
            <el-select v-model="params.province" placeholder="省" clearable style="width: 100%">
              <el-option
                v-for="item in systemDicts.sys_province"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="driveType">
            <el-select
              v-model="params.driveType"
              placeholder="驱动形式"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in systemDicts.sys_drive_form"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="cylinders">
            <el-select
              v-model="params.cylinders"
              placeholder="气缸数"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in systemDicts.sys_cylinder_count"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col v-if="params.dataSource === '1'" :xs="8" :sm="8" :md="3">
          <el-form-item prop="dataType">
            <el-select
              v-model="params.dataType"
              multiple
              placeholder="数据扩展"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in dictDataType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="3">
          <el-form-item>
            <el-button type="primary" @click="toggleSearch">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-segmented
      ref="segmentedRef"
      v-model="params.subMarket"
      :options="data.defaultDictsSubMarketAndBreed"
      @change="toggleSearch('segmented')"
      class="segmented"
    >
      <template #default="scope">
        <div>{{ scope.item.label }}</div>
      </template>
    </el-segmented>
  </div>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType, dictDataType } from '@/utils/common/dicts.js'
// import formValidate from '@/utils/hooks/formValidate.js'
import useInnerData from '@/utils/hooks/innerData.js'

const store = useStore()
// 快捷查询按钮
// 牵引车/载货车/专用车/自卸车皮卡 的细分市场1都是卡车
const defaultDictsSubMarketAndBreed = [
  {
    label: '总体',
    value: 'all'
  },
  {
    label: '卡车',
    value: '卡车'
  },
  {
    label: '客车',
    value: '客车'
  },
  {
    label: '牵引车',
    value: '牵引车'
  },
  {
    label: '载货车',
    value: '载货车'
  },
  {
    label: '专用车',
    value: '专用车'
  },
  {
    label: '自卸车',
    value: '自卸车'
  },
  {
    label: '皮卡',
    value: '皮卡'
  }
]

const defaultDictsSubMarketAndBreedHuoyun = [
  // {
  //   label: '总体',
  //   value: 'all'
  // },
  {
    label: '卡车',
    value: '卡车'
  },
  {
    label: '牵引车',
    value: '牵引车'
  },
  {
    label: '载货车',
    value: '载货车'
  },
  {
    label: '专用车',
    value: '专用车'
  },
  {
    label: '自卸车',
    value: '自卸车'
  },
  {
    label: '皮卡',
    value: '皮卡'
  }
]

const systemDicts = computed(() => store.state.dicts.dicts)
const emit = defineEmits(['change'])

const segmentedRef = ref(null)
const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
      year: (new Date().getFullYear() - 1).toString(), // 年份
      segment: '商用车', // 板块
      dataSource: dataSource, // 数据来源 (1/6)
      province: '', // 省份
      engineFactory: '', // 发动机厂
      manuFacturer: '', // 主机厂
      driveType: '', // 驱动形式(需确认)
      weightMidLight: '', // 车型 重中轻
      fuelType: '', // 燃料
      subMarket1: '',
      subMarket: '', // all-总体；卡车-卡车；客车-客车；牵引车-牵引车……
      breed: '', // 品系：牵引车、载货车、专用车、自卸车、皮卡。选择总体、卡车、客车时不填，其他必填。
      cylinders: '', // 气缸数
      dataType: []
      // month: '12', // 月
      // quarter: '' // 季度
    })
  }
})
const data = reactive({
  linkageData: [], // 多级联动数据
  defaultDictsSubMarketAndBreed: defaultDictsSubMarketAndBreed
})
const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const { initDateRange, innerdate, disabledFeatureDate } = useInnerData(params, toggleSearch)
// 指标类型(0-月，2-月累，1-季度)
watch(
  () => params.pointerType,
  val => {
    if (val === '1') {
      params.month = ''
    } else {
      params.quarter = ''
    }
    innerdate()
  }
)

watch(
  () => params.dataSource,
  val => {
    switch (val) {
      case '1':
        // 上险数
        initDateRange('上险数')
        changeDataSource('上险数')

        params.segment = '商用车'
        params.subMarket1 = ''
        break
      case '6':
        // 货运新增数
        initDateRange('货运新增数')
        changeDataSource('货运新增数')

        params.segment = '商用车'
        params.subMarket1 = ''
        break
    }
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
async function toggleSearch(ev) {
  if (params.dataSource !== '1') {
    params.dataType = []
  }
  if (ev === 'segmented') {
    const val = params.subMarket
    const subMarket = ['all', '卡车', '客车']
    if (subMarket.findIndex(i => i === val) !== -1) {
      params.breed = ''
      params.subMarket1 = val === 'all' ? '' : val
    } else {
      // 非subMarket的值当前tabs 牵引车/载货车/专用车/自卸车皮卡 的细分市场1都是卡车
      params.breed = val
      params.subMarket1 = '卡车'
    }
  } else {
    const sameKeyArray = ['', '客车']
    if (sameKeyArray.indexOf(params.subMarket1) !== -1) {
      params.subMarket = params.subMarket1 === '' ? 'all' : params.subMarket1
      params.breed = ''
    } else if (params.subMarket1 === '卡车' && params.breed === '') {
      params.subMarket = '卡车'
    }
  }
  await nextTick()
  const data = JSON.parse(JSON.stringify(toRaw(params)))
  delete data.subMarket2
  emit('change', data)
}
/**
 * 改变数据源的时候改变tabs快捷搜索
 * @param dataSource 数据源
 */
function changeDataSource(dataSource) {
  if (dataSource === '上险数') {
    data.defaultDictsSubMarketAndBreed = defaultDictsSubMarketAndBreed
  } else if (dataSource === '货运新增数') {
    data.defaultDictsSubMarketAndBreed = defaultDictsSubMarketAndBreedHuoyun
  }
  params.subMarket = 'all'
  params.breed = ''
  params.subMarket1 = ''
}
const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['上险数', '货运新增数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}

initDateRange('货运新增数', true)
getDictsData()
</script>

<style scoped lang="scss">
.search-form {
  margin-bottom: 0;
}
.segmented {
  margin: 10px 0;
}
</style>
