<template>
  <div class="table-list">
    <el-form :model="data.params" ref="formRef" label-width="0" class="table-list__search">
      <el-row :gutter="16">
        <el-col :xs="8" :sm="8" :md="6">
          <el-form-item prop="dateRange">
            <el-date-picker
              v-model="data.params.dateRange"
              type="daterange"
              clearable
              filterable
              range-separator="-"
              value-format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placeholder="请输入起始年月"
              :disabledDate="disabledFeatureDate"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3"
          ><el-form-item prop="searchKey">
            <el-input v-model="data.params.searchKey" placeholder="关键字" />
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3"
          ><el-form-item prop="multiSection">
            <el-select
              v-model="data.params.multiSection"
              clearable
              style="width: 100%"
              placeholder="多栏目"
            >
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 新增状态选择 -->
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="publishStatus">
            <el-select
              v-model="data.params.publishStatus"
              clearable
              style="width: 100%"
              placeholder="状态"
            >
              <el-option label="草稿" :value="0" />
              <el-option label="已发布" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 新增栏目输入框 -->
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="lmName">
            <el-select
              v-model="data.params.lmName"
              placeholder="所属栏目"
              clearable
              style="width: 100%"
              @change="selectFilterData"
              filterable
            >
              <el-option
                v-for="item in data.newTypeDicts"
                :key="item?.value"
                :label="item?.label"
                :value="item?.label"
              />
            </el-select>
            <!-- <el-input v-model="data.params.lmName" placeholder="栏目名称" /> -->
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item>
            <div class="search-form__button">
              <el-button type="primary" @click="handleQuery">搜索</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table-list__control">
      <el-button type="primary" @click="handleUpdate({ id: '' })">新增</el-button>
      <el-button type="warning" @click="handleBatchAssignKind" :disabled="!data.selectedRows.length"
        >批量分配栏目</el-button
      >
      
      <el-button type="success" @click="handleBatchPublish" :disabled="!data.selectedRows.length"
        >批量发布</el-button
      >
      <el-button type="info" @click="handleBatchCancelPublish" :disabled="!data.selectedRows.length"
        >批量取消发布</el-button
      >
      <el-button type="danger" @click="handleBatchDelete" :disabled="!data.selectedRows.length"
        >批量删除</el-button
      >
    </div>
    <el-table
      v-loading="loading.table"
      height="100%"
      :data="data.list"
      :border="true"
      class="table-list__content"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column label="标题" prop="title" show-overflow-tooltip />
      <el-table-column label="来源" prop="url" show-overflow-tooltip />
      <el-table-column label="栏目" prop="lmName" width="150" show-overflow-tooltip />
      <el-table-column label="发布时间" prop="time" width="150" show-overflow-tooltip />

      <el-table-column label="状态" prop="publishStatus" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.publishStatus === 1 ? 'success' : 'info'">
            {{ scope.row.publishStatus === 1 ? '已发布' : '草稿' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="scope">
          <div class="table-list__content--control" >
            <el-popover
              class="box-item"
              placement="left"
              width="800"
              v-if="scope.row.html"
            >
              <template #reference>
                <el-icon><View /></el-icon>
              </template>
              <div
                style=" height: 260px; overflow: auto"
                v-html="scope.row.html"
                 :key="scope.row.id" 
              ></div>
            </el-popover>

            <!-- <el-tooltip style="width: 400px;" :content="scope.row.html" raw-content>
              <el-icon><View /></el-icon>
            </el-tooltip> -->
            <el-button type="primary" text icon="Edit" @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button type="danger" text icon="Delete" @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button type="primary" text icon="Tickets" @click="handleDetail(scope.row)"
              >查看</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <BiPagination
      :total="data.total"
      v-model:page="data.params.pageNum"
      v-model:limit="data.params.pageSize"
      @pagination="getList"
    />
    <submitDialog
      v-model="data.submit.flag"
      :data="data.submit.data"
      @success="getList"
      :newTypeDicts="data.newTypeDicts"
    />
    <infoDialog v-model="data.info.flag" :query="data.info.data" />
    <assinClass v-model="BatchAssignKind" :newTypeDicts="data.newTypeDicts" :ids="data.selectedRows" @success="handleSuccess" />
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import BiPagination from '@/views/components/BiPagination.vue'
import submitDialog from './components/submitDialog.vue'
import infoDialog from '@/views/home/<USER>/infoDialog.vue'
import assinClass from './components/assinClass.vue'
import { list } from '@/api/database/labels.js'

import formValidate from '@/utils/hooks/formValidate.js'
import { newsList, delNews, batchPublish, batchCancelPublish ,deleteBatchApi} from '@/api/intelligence/newsinfo.js'

const { proxy } = getCurrentInstance()
const BatchAssignKind = ref(false);
const formRef = ref(null)
const getCurrentMonthRange = () => {
  const now = new Date()
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
  return [formatDate(firstDay), formatDate(now)]
}

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
const data = reactive({
  submit: {
    flag: false,
    data: {}
  },
  info: {
    flag: false,
    data: {}
  },
  params: {
    // dateRange: [], // 前端展示用
    dateRange: getCurrentMonthRange(), // 设置为当前月的范围, // 前端展示用
    startDate: '',
    endDate: '',
    searchKey: '',
    pageNum: 1,
    pageSize: 15,
    multiSection: '',
    publishStatus: 0, // 状态查询参数
    lmName: '' // 栏目查询参数
  },
  list: [],
  total: 0,
  newTypeDicts: [],
  selectedRows: [] // 新增：存储选中的行
})
const loading = reactive({
  table: false,
  delete: false
})
const { disabledFeatureDate } = formValidate()

/** 查询列表 */
async function getList() {
  if (loading.table) return
  loading.table = true
  const params = JSON.parse(JSON.stringify(data.params))
  if (data.params.dateRange && data.params.dateRange.length > 0) {
    params.startDate = data.params.dateRange[0]
    params.endDate = data.params.dateRange[1]
  } else {
    params.startDate = ''
    params.endDate = ''
  }
  const res = await newsList(params).catch(e => e)
  loading.table = false
  if (res.code !== 200) return proxy.$modal.msgError(res.msg)
  data.list = res.rows
  data.total = res.total
}

async function handleDelete(ev) {
  const msgFlag = await ElMessageBox.confirm(`确定删除“${ev.title}”`, '提示', {
    showClose: false,
    type: 'warning'
  }).catch(e => e)
  if ('confirm' !== msgFlag) return
  if (loading.delete) return
  loading.delete = true
  let formData = new FormData()
  formData.append('id', ev.id)
  const res = await delNews(formData).catch(e => e)
  loading.delete = false
  ElMessageBox.alert(res.msg, '提示', {
    showClose: false,
    callback: () => {
      getList()
    }
  })
}

function handleDetail(ev) {
  let newsTagId = ''
  if (ev.iisSysNewsTagId && typeof ev.iisSysNewsTagId === 'string' && ev.iisSysNewsTagId.length > 0)
    newsTagId = ev.iisSysNewsTagId.split(',')[0]
  data.info.data = { newsInfoId: ev.id, newsTagId,pageSearchKey: data.params.searchKey}
  data.info.flag = true
}
/** 搜索按钮操作 */
function handleQuery() {
  data.params.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  formRef.value.resetFields()
  data.params.dateRange = getCurrentMonthRange();
  handleQuery()
}

/** 修改按钮操作 */
function handleUpdate(ev) {
  data.submit.data = { id: ev.id }
  data.submit.flag = true
}
async function getNewsTypeDicts() {
  const res = await list({ pageNum: 1, pageSize: 10000 }).catch(e => e)
  if (res.code !== 200) return
  const rows = res.rows
  const newTypeDicts = []
  rows.forEach(el => {
    newTypeDicts.push({
      label: `${el.dictModuleName}/${el.dictLeftMenuName}/${el.dictNewsName}/${el.newsSection}`,
      value: el.id ? el.id.toString() : ''
    })
  })
  data.newTypeDicts = newTypeDicts
}

// 新增：处理选中行变化
function handleSelectionChange(rows) {
  data.selectedRows = rows
}
async function handleBatchPublish() {
  const msgFlag = await ElMessageBox.confirm(
    `确定发布选中的 ${data.selectedRows.length} 新闻吗？`,
    '提示',
    {
      showClose: false,
      type: 'warning'
    }
  ).catch(e => e)
  if ('confirm' !== msgFlag) return
  const ids = data.selectedRows.map(row => row.id)
  // console.log('选中的行：', data.selectedRows.map(row => row.id));
  batchPublish({ ids })
    .then(res => {
      ElMessageBox.alert(res.msg, '提示', {
        showClose: false,
        callback: () => {
          data.selectedRows = []

          getList()
        }
      })
    })
    .catch(error => {
      console.log(error)
      // proxy.$modal.msgError(error.message || '删除操作异常')
    })
}

async function handleBatchCancelPublish() {
  const msgFlag = await ElMessageBox.confirm(
    `确定取消发布选中的 ${data.selectedRows.length} 新闻吗？`,
    '提示',
    {
      showClose: false,
      type: 'warning'
    }
  ).catch(e => e)
  if ('confirm' !== msgFlag) return
  const ids = data.selectedRows.map(row => row.id)
  // console.log('选中的行：', data.selectedRows.map(row))
  batchCancelPublish({ ids })
    .then(res => {
      ElMessageBox.alert(res.msg, '成功', {
        showClose: false,
        callback: () => {
          data.selectedRows = []

          getList()
        }
      })
    })
    .catch(error => {
      console.log(error)
    })
}


async function handleBatchDelete() {
  const msgFlag = await ElMessageBox.confirm(
    `确定删除选中的 ${data.selectedRows.length} 新闻吗？`,
  )
  if ('confirm'!== msgFlag) return
  const ids = data.selectedRows.map(row => row.id)
  deleteBatchApi({ ids })
   .then(res => {
      ElMessageBox.alert(res.msg, '成功', {
        showClose: false,
        callback: () => {
          data.selectedRows = []
          getList()
        }
      })
    })
    .catch(error => {
      console.log(error)
    })
}
async function handleBatchAssignKind() {
  BatchAssignKind.value = true;
}
async function handleSuccess() {
data.selectedRows  =[];
  getList()
}
getNewsTypeDicts()
getList()
</script>
