<template>
  <div class="wrap bi-tabs">
    <el-page-header @back="backPage" class="bi-page-header">
      <template #content>
        <WordHighlighter
          :query="data.searchKey"
          :highlightStyle="{ 'font-weight': 'bolder', background: '#ffff00' }"
          >{{ data.title }}</WordHighlighter
        >
      </template>
      <div v-if="data.newsInfoId || !props.isPage" class="content">
        <el-descriptions>
          <el-descriptions-item label="【发布时间】">{{ data.time }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions>
          <el-descriptions-item label="【来源】">
            <span @click="goToExternalUrl(data.url)" class="url-address">{{ data.url }}</span
            ><span v-if="data.site"> ({{ data.site }})</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="sore">
          <div style="margin-right: 20px">
            <span class="mingTitle">命中功能关键字：</span>
            <span style="font-weight: bolder; background: #ffff00">{{
              data?.detail?.functionKey
            }}</span>
          </div>
          <div>
            <span class="mingTitle">命中信息种类关键字：</span>
            <span style="font-weight: bolder; background: #ffff00">{{
              data?.detail?.matchKey
            }}</span>
          </div>
          <div>
            <span class="mingTitle">查询关键字：</span>
            <span style="font-weight: bolder; background: limegreen">{{
                props.pageQuery.pageSearchKey
            }}</span>
          </div>
        </div>

        <el-descriptions>
          <el-descriptions-item>
            <div v-html="data.html" />
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-else class="content">
        <el-descriptions>
          <el-descriptions-item label="【发布时间】">{{ data.createTime }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions>
          <el-descriptions-item v-if="data.url" label="【来源】">
            <span @click="goToExternalUrl(data.url)" class="url-address">{{ data.url }}</span
            ><span v-if="data.site"> ({{ data.site }})</span>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions>
          <el-descriptions-item>
            <div class="content">
              <WordHighlighter
                :query="data.searchKey"
                :highlightStyle="{ 'font-weight': 'bolder', background: '#ffff00' }"
                >{{ data.infoContent }}</WordHighlighter
              >
            </div>
          </el-descriptions-item>
        </el-descriptions>
        <el-table
          v-if="data.hasAttachment === '1'"
          v-loading="table.loading"
          :data="table.data"
          style="width: 100%"
        >
          <el-table-column prop="name" label="文件名" />
          <!-- <el-table-column prop="updateTime" width="150" label="日期" /> -->
          <el-table-column width="140" label="操作">
            <template #default="{ row }">
              <el-button-group style="margin-left: -12px">
                <el-button
                  v-hasPermi="['home:onenews:download']"
                  :loading="row.loading"
                  type="primary"
                  @click="downloadWorld(row)"
                  text
                >
                  下载
                </el-button>
                <!-- v-if="previewType.some(e => e === row.fileFormat)" -->
                <el-button
                  type="primary"
                  v-hasPermi="['home:onenews:preview']"
                  @click="previewResource(row)"
                  text
                >
                  预览
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-page-header>
  </div>
</template>

<script setup>
import WordHighlighter from 'vue-word-highlighter'
import { viewNewsInfo } from '@/api/intelligence/newsinfo.js'
import { getAttachmenList, getAttachmenDetail } from '@/api/intelligence/info.js'
import { Base64 } from 'js-base64'
import { removeDataAnchor } from "@/utils/common/method.js"
const store = useStore()
const getters = computed(() => store.getters)

const router = useRouter()
const route = useRoute()
const emit = defineEmits(['close'])
const query = route.query
const data = reactive(query)
const props = defineProps({
  isPage: {
    type: Boolean,
    default: true
  },
  pageQuery: {
    type: Object,
    default: () => ({ newsInfoId: '', newsTagId: '', searchKey: '',pageSearchKey: '' })
  }
})
const table = reactive({
  data: [],
  loading: false
})
// 可以预览的文件类型
const previewType = ref(['png', 'jpg', 'jpeg', 'mp4', 'pdf', 'webp', 'gif', 'jfif', 'html'])
watch(
  () => props.pageQuery,
  ev => {
    if (!props.isPage) {
      data.newsInfoId = ev?.newsInfoId
      data.newsTagId = ev?.newsTagId
      data.searchKey = ev?.searchKey
      initInfo()
    }
  },
  {
    deep: true,
    immediate: true
  }
)
const goToExternalUrl = url => {
  window.open(url, '_blank')
}
async function initInfo() {
  data.title = ''
  data.time = ''
  data.url = ''
  data.site = ''
  data.html = ''
  data.detail = ''
  const queryParams = !props.isPage ? toRaw(props.pageQuery) : query
  let params = { id: queryParams.newsInfoId, newsTagId: queryParams?.newsTagId }
  const res = await viewNewsInfo(params).catch(e => e)
  if (res?.code !== 200) return
  data.title = res.data.title
  data.time = res.data.time
  data.url = res.data.url
  data.site = res.data.site
  if (data.searchKey && res.data.html) {
    const reg = new RegExp(data.searchKey, 'g')
    res.data.html = res.data.html.replace(
      reg,
      `<span style='font-weight:bolder;background: #ffff00'>${data.searchKey}</span>`
    )
  }
  if (res.data?.matchKey) {
    const keyArray = res.data.matchKey.split('、')
    keyArray.forEach(el => {
      const reg = new RegExp(el, 'g')
      res.data.html = res.data.html.replace(
        reg,
        `<span style='font-weight:bolder;background: #ffff00'>${el}</span>`
      )
    })
  }
  if (res.data?.functionKey) {
    const keyArray = res.data.functionKey.split('、')
    keyArray.forEach(el => {
      const reg = new RegExp(el, 'g')
      res.data.html = res.data.html.replace(
        reg,
        `<span style='font-weight:bolder;background: #ffff00'>${el}</span>`
      )
    })
  }
  if (props.pageQuery.pageSearchKey) {
    const keyArray = props.pageQuery.pageSearchKey.split(' ')
    keyArray.forEach(el => {
      const reg = new RegExp(el, 'g')
      res.data.html = res.data.html.replace(
          reg,
          `<span style='font-weight:bolder;background: limegreen'>${el}</span>`
      )
    })
  }

  res.data.html =  removeDataAnchor(res.data.html)

  data.html = res.data.html
  data.detail = res.data
}

async function getFileList() {
  table.loading = true
  const res = await getAttachmenList({ id: query.id }).catch(e => e)
  if (res.code !== 200) return (table.loading = false)
  table.data = res.data
  table.loading = false
}
const backPage = () => {
  if (props.isPage) {
    router.replace(`/home/<USER>
  } else {
    emit('back', true)
    data.title = ''
    data.time = ''
    data.url = ''
    data.site = ''
    data.html = ''
    data.detail = ''
  }
}

async function downloadWorld(row) {
  const { name, id } = row
  let { code, data } = await getAttachmenDetail({ id })
  if (code !== 200) return
  if (row.loading) return
  row.loading = true
  var request = new XMLHttpRequest()
  request.responseType = 'blob'
  request.open('GET', data.path)
  request.onload = function () {
    var url = window.URL.createObjectURL(this.response)
    var a = document.createElement('a')
    document.body.appendChild(a)
    a.href = url
    a.download = name
    a.click()
    row.loading = false
  }
  request.send()
}

/**
 * 预览资源文件
 * @param {Object} param0 - 包含资源ID的对象
 * @param {string} param0.id - 资源ID
 */
const previewResource = async ({ id }) => {
  // 判断当前是否为开发环境
  const isDev = import.meta.env.DEV;
  // 根据环境设置域名：开发环境使用测试域名，生产环境使用当前域名
  const domain = isDev ? "http://iis-spider.qas.yuchai.com" : window.location.origin;
  // 构建预览URL基础部分
  let url = domain + '/apaasFile/onlinePreview?url='
  // 获取附件详细信息
  let { code, data } = await getAttachmenDetail({ id })
  if (code !== 200) return
  // 解析附件路径URL
  const urlX = new URL(data.path);
  // 对Signature参数进行URL编码
  urlX.searchParams.set('Signature', encodeURIComponent(urlX.searchParams.get('Signature')));
  // 生成水印文本（用户名+登录日期）并进行URL编码
  let  watermarkTxt =encodeURIComponent(`${getters.value.nickName}${getters.value.name}`);
  // 构建完整预览URL：基础URL + 编码后的附件路径 + 水印参数
  url = url + encodeURIComponent(Base64.encode(urlX.toString())) + '&watermarkTxt=' + watermarkTxt
  // 在新窗口打开预览
  window.open(url, '_blank')
}
// newsInfoId代表是接口请求详情
if (query.newsInfoId && props.isPage) {
  initInfo()
} else if (!query.newsInfoId && props.isPage) {
  getFileList()
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.wrap {
  width: calc(100% - $bi-layout-margin * 2);
  margin: $bi-layout-margin;
  height: $bi-main-height-margin;
  background: linear-gradient(0deg, #d2e6fc 0%, rgba(210, 230, 252, 0.5) 100%);
  :deep(.el-page-header__header) {
    height: 50px;
    border: 1px solid;
    border-image: linear-gradient(
        270deg,
        rgba(138, 188, 249, 0) 0%,
        #83b5f3 51%,
        rgba(131, 181, 243, 0) 99%,
        rgba(131, 181, 243, 0) 99%
      )
      1;
  }
  :deep(.el-page-header__back) {
    height: 42px;
    margin: 4px;
    padding: 0 16px;
    background: #fff;
    border-radius: 8px;
    font-size: 16px;
  }
  :deep(.el-divider) {
    border: none;
  }
  :deep(.el-page-header__main) {
    height: calc($bi-main-height-margin - $bi-layout-margin - 68px);
    margin: $bi-layout-margin;
    padding: 20px 16px;
    border: none;
    overflow: auto;
    background: #fff;
  }
  :deep(.el-collapse-item__header) {
    padding: $bi-layout-margin;
    font-size: 18px;
    color: #fff;
    background: $bi-form-button;
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 0px;
  }
  .list-item {
    height: 32px;
    line-height: 32px;
    padding: 0 $bi-layout-margin;
    font-size: 16px;
    color: $bi-font-color;
    cursor: pointer;
  }
}
.url-address {
  color: #145cbc;
  cursor: pointer;
}

:deep(.el-descriptions__content:not(.is-bordered-label)) {
  color: #000;
}

.sore {
  display: flex;
  padding: 20px 0px;
  padding-left: 4px;
  background: #fff;
  .mingTitle {
    font-size: 20px;
    font-weight: 550 !important;
  }
}
</style>
