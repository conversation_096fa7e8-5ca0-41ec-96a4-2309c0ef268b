<template>
  <CommonTabs :hideList="[]" active="1">
    <template #searchArea>
      <SearchFormResource :params="originParams" @change="getParams" />
    </template>
    <el-row :gutter="20" style="margin-left: 0; margin-right: 0">
      <el-col :xs="24" :sm="24" :md="8">
        <chart
          v-loading="data.chartA.loading"
          :title="{ text: data.chartA.title }"
          :series="data.chartA.data"
          height="270px"
          :options="{
            tooltip: {
              formatter: params =>
                TooltipFormatter(TooltipPercentageComponent, params, {
                  sortField: 'value'
                })
            },
            yAxis: [{ name: '单位：(%)', axisLabel: { formatter: '{value}%' }, max: 100, min: 0 }]
          }"
          :precision="1"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <chart
          v-loading="data.chartB.loading"
          :title="{ text: data.chartB.title }"
          :series="data.chartB.data"
          :options="{
            tooltip: { formatter: formatterChartB },
            yAxis: [{ name: '单位：(辆)', minInterval: 1 }, ...yAxisRight]
          }"
          height="270px"
          :precision="0"
          show-total
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <chart
          v-loading="data.chartC.loading"
          :title="{ text: data.chartC.title }"
          :options="{
            tooltip: { formatter: params => TooltipFormatter(TooltipPercentageComponent, params,{
                  sortField: 'value'
                }) },
            yAxis: [{ name: '单位：(%)', axisLabel: { formatter: '{value}%' }, max: 100, min: 0 }]
          }"
          :series="data.chartC.data"
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <chart
          v-loading="data.chartD.loading"
          :title="{ text: data.chartD.title }"
          :options="{
            tooltip: { formatter: params => TooltipFormatter(TooltipPercentageComponent, params,{
                  sortField: 'value'
                }) },
            yAxis: [{ name: '单位：(%)', axisLabel: { formatter: '{value}%' }, max: 100, min: 0 }]
          }"
          :series="data.chartD.data"
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <chart
          v-loading="data.chartE.loading"
          :title="{ text: data.chartE.title }"
          :options="{
            tooltip: { formatter: params => TooltipFormatter(TooltipJgqsPercentageComponent, params,{
                  sortField: 'value'
                }) },
            yAxis: [{ name: '单位：(%)', axisLabel: { formatter: '{value}%' }, max: 100, min: 0 }]
          }"
          :series="data.chartE.data"
          yAxisLabelFormate="{value}%"
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <chart
          v-loading="data.chartF.loading"
          :title="{ text: data.chartF.title }"
          :options="{
            tooltip: {
              formatter: params =>
                TooltipFormatter(TooltipPercentageComponent, removeYuChai(params),{
                  sortField: 'value'
                })
            },
            yAxis: [{ name: '单位：(%)' }, ...yAxisRight]
          }"
          :series="data.chartF.data"
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="24">
        <chart
          v-loading="data.chartG.loading"
          :title="{ text: data.chartG.title }"
          :options="{
            tooltip: { formatter: params => TooltipFormatter(TooltipComponent, params,{
                  sortField: 'value'
                }) },
            yAxis: [{ name: '单位：(辆)', minInterval: 1 }, ...yAxisRight]
          }"
          :series="data.chartG.data"
          height="270px"
        />
      </el-col>
    </el-row>
  </CommonTabs>
</template>

<script setup lang="jsx">
import CommonTabs from '@/views/components/tabs/CommonTabs'
import SearchFormResource from './components/SearchFormResource.vue'
import chart from '@/views/components/echarts/chart.vue'
import provinceFull2Jian from '@/utils/common/map/provinceFull2Jian.json'
import calChartsData from '@/utils/hooks/calChartsData.js'
import { numberFormat } from '@/utils/format'
import {
  commVehicleSalesTrendApi,
  commVehicleFuelTrendApi,
  commVehicleMatchTrendApi,
  commVehicleAreaSales
} from '@/api/machine/car'
import Tpis from '@/views/components/tooltip/index.vue'

import {
  TooltipPercentageComponent,
  TooltipComponent,
  TooltipJgqsPercentageComponent
} from '@/views/components/jsx/TooltipComponent.jsx'
import { TooltipFormatter } from '@/utils/common/method'

const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '上险数')?.value
// 初始化搜索条件
const originParams = {
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  quarter: '', // 季度
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  dataSource: dataSource, // 数据来源（货运新增）
  subMarket1: '卡车', // 细分市场1
  manuFacturerTabs: '徐工',
  manuFacturer: '徐工', // 主机厂
  engineFactory: '', // 发动机厂
  fuelType: '', // 燃料
  weightMidLight: '', // 车型
  cylinders: '', // 气缸数
  province: '', // 省
  dataType: [],
  segment: '商用车' // 板块
}
const data = reactive({
  params: { ...originParams },
  chartA: {
    title: '',
    data: [],
    loading: false
  },
  chartB: {
    title: '',
    data: [],
    loading: false
  },
  chartC: {
    title: '',
    data: [],
    loading: false
  },
  chartD: {
    title: '',
    data: [],
    loading: false
  },
  chartE: {
    title: '',
    data: [],
    loading: false
  },
  chartF: {
    title: '',
    data: [],
    loading: false
  },
  chartG: {
    title: '',
    data: [],
    loading: false
  }
})
const { yAxisRight, referData, setOneArraySeriesData, sortByArray, sortRankByFirstBarData } =
  calChartsData()
function getParams(params) {
  data.params = params
  initChartData()
}

/**
 * @description 处理接口数据
 * @param params 搜索参数
 */
const initChartData = async () => {
  data.chartA.loading = true
  data.chartB.loading = true
  data.chartC.loading = true
  data.chartD.loading = true
  data.chartE.loading = true
  data.chartF.loading = true
  data.chartG.loading = true
  const monthSortArray = referData.monthSort
  const quarterSortArray = referData.quarterSort
  const params = JSON.parse(JSON.stringify(data.params))
  params.dataType = params.dataType ? params.dataType.join() : ''
  params.province = provinceFull2Jian[params.province]
  if (!params.province) params.province = ''
  // 第一个图表
  commVehicleSalesTrendApi(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      const { salesLineList: lineB, salesTrendList: barB, salesYearList: barA } = resData
      console.log('数据图表2', barB)
      // a.处理左边按销量品系占⽐显示
      barA.forEach(el => {
        el.xAxisName = el.year + '年'
        el.tooltipValue = `${el.prop ?? 0}%`
      })
      let seriesBarA = setOneArraySeriesData({
        list: barA,
        xAxisKey: 'xAxisName',
        yAxisKey: 'prop',
        legendKey: 'breed'
      })
      const currentYear = Number(params.year)
      const yearSort = []
      for (let i = 0; i < 3; i++) {
        yearSort.unshift(`${currentYear - i}年`)
      }

      seriesBarA = sortByArray(seriesBarA, yearSort, 'xAxisName')
      seriesBarA = sortRankByFirstBarData(seriesBarA, true, 'last')
      seriesBarA.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      data.chartA.data = seriesBarA
      data.chartA.title = `${params.manuFacturer}年度细分市场销量趋势`
      // b.右边堆叠图按销量显示⾼度，⿏标悬浮显示同⽐、销量、占⽐; 折线显示同⽐中当前选择的主机⼚
      barB.forEach(el => {
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        } else {
          el.month = Number(el.month)
          el.xAxisName = el.month + '月'
        }
        el.totalUnit = '辆'
        el.tooltipValue = !el.sales
          ? ''
          : `销量:${numberFormat(el.sales, 0) ?? 0}辆;同比:${numberFormat(el.propChange_px, 1) ?? 0}%;占比:${numberFormat(el.prop, 1) ?? 0}%`
      })
      let seriesBarB = setOneArraySeriesData({
        list: barB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'sales',
        legendKey: 'breed'
      })
      seriesBarB = sortByArray(
        seriesBarB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesBarB.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      seriesBarB = sortRankByFirstBarData(seriesBarB)
      lineB.forEach(el => {
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        } else {
          el.month = Number(el.month)
          el.xAxisName = el.month + '月'
        }
        el.propChange = el.propChange === null || el.propChange === undefined ? '' : el.propChange
        el.legendName = '同比'
        el.totalUnit = '辆'
        el.tooltipValue =
          el.propChange === null || el.propChange === undefined ? '' : `${el.propChange ?? 0}%`
      })
      let seriesLineB = setOneArraySeriesData({
        list: lineB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'propChange',
        legendKey: `legendName`
      })
      seriesLineB = sortByArray(
        seriesLineB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesLineB.forEach(el => {
        el.type = 'line'
        el.yAxisIndex = 1
        el.stack = null
      })

      data.chartB.data = [...seriesBarB, ...seriesLineB]
      data.chartB.title = `${params.manuFacturer}${params.pointerType === '1' ? '季度' : params.pointerType === '2' ? '月累' : '月度'}细分市场销量趋势`
    })
    .finally(() => {
      data.chartA.loading = false
      data.chartB.loading = false
    })
  // 第二个图表
  commVehicleFuelTrendApi(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      const { fuelTrendList: barB, fuelYearList: barA } = resData
      console.log('未处理', barB)

      // a.处理左边燃料占⽐
      barA.forEach(el => {
        el.xAxisName = el.year + '年'
        el.tooltipValue = el.prop ? `${el.prop}%` : ''
      })
      let seriesBarA = setOneArraySeriesData({
        list: barA,
        xAxisKey: 'xAxisName',
        yAxisKey: 'prop',
        legendKey: 'fuelType'
      })
      const currentYear = Number(params.year)
      const yearSort = []
      for (let i = 0; i < 3; i++) {
        yearSort.unshift(`${currentYear - i}年`)
      }

      seriesBarA = sortByArray(seriesBarA, yearSort, 'xAxisName')
      seriesBarA = sortRankByFirstBarData(seriesBarA, true, 'last')
      seriesBarA.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      data.chartC.data = seriesBarA
      data.chartC.title = `${params.manuFacturer}年度燃料结构趋势`

      // b.右边按查询条件显示燃料占⽐
      barB.forEach(el => {
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        } else {
          el.month = Number(el.month)
          el.xAxisName = el.month + '月'
        }
        el.tooltipValue = el.prop ? `${el.prop}%` : ''
      })
      let seriesBarB = setOneArraySeriesData({
        list: barB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'prop',
        legendKey: 'fuelType'
      })
      seriesBarB = sortByArray(
        seriesBarB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesBarB = sortRankByFirstBarData(seriesBarB)
      seriesBarB.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      data.chartD.data = seriesBarB
      data.chartD.title = `${params.manuFacturer}${params.pointerType === '1' ? '季度' : params.pointerType === '2' ? '月累' : '月度'}燃料结构趋势`
    })
    .finally(() => {
      data.chartC.loading = false
      data.chartD.loading = false
    })
  // 第三个图表
  commVehicleMatchTrendApi(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      let { matchLineList: lineB, matchTrendList: barB, matchYearList: barA } = resData
      // a.处理左边燃料占⽐
      if (!barA) barA = []
      if (!barB) barB = []
      if (!lineB) lineB = []
      barA.forEach(el => {
        el.tooltipValue = `${el.prop}%`
        el.xAxisName = el.year + '年'
      })
      let seriesBarA = setOneArraySeriesData({
        list: barA,
        xAxisKey: 'xAxisName',
        yAxisKey: 'prop',
        legendKey: 'engine'
      })
      const currentYear = Number(params.year)
      const yearSort = []
      for (let i = 0; i < 3; i++) {
        yearSort.unshift(`${currentYear - i}年`)
      }

      seriesBarA = sortByArray(seriesBarA, yearSort, 'xAxisName')
      seriesBarA = sortRankByFirstBarData(seriesBarA, true, 'last')
      seriesBarA.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      data.chartE.data = seriesBarA
      data.chartE.title = `${params.manuFacturer}年度动力配套结构趋势`
      barB.forEach(el => {
        el.tooltipValue = `${el.prop}%`
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        } else {
          el.month = Number(el.month)
          el.xAxisName = el.month + '月'
        }
      })
      let seriesBarB = setOneArraySeriesData({
        list: barB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'prop',
        legendKey: 'engine'
      })
      seriesBarB = sortByArray(
        seriesBarB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesBarB = sortRankByFirstBarData(seriesBarB)
      seriesBarB.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      let seriesLineB = []
      if (lineB && lineB.length > 0) {
        // 获取top1的项
        const topOneArray = barA.filter(e => e.year == params.year).sort((a, b) => a.top - b.top)
        let topOneKey = ''
        if (topOneArray.length > 0) topOneKey = topOneArray[0].engine
        let originLineB = []
        let legendName = ''
        if (lineB[0]['玉柴']) {
          originLineB = lineB[0]['玉柴']
          legendName = '玉柴占比'
        } else if (topOneKey && lineB[0][topOneKey]) {
          originLineB = lineB[0][topOneKey]
          legendName = `${topOneKey}占比`
        }

        originLineB.forEach(el => {
          // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
          if (params.pointerType === '1') {
            el.xAxisName = referData.quarterRefer[el.month]
          } else {
            el.month = Number(el.month)
            el.xAxisName = el.month + '月'
          }
          el.legendName = legendName
          el.tooltipValue = `${el.prop}%`
          // el.totalUnit = `辆`
        })
        seriesLineB = setOneArraySeriesData({
          list: originLineB,
          xAxisKey: 'xAxisName',
          yAxisKey: 'prop',
          legendKey: 'legendName'
        })
        seriesLineB = sortByArray(
          seriesLineB,
          params.pointerType === '1' ? quarterSortArray : monthSortArray,
          'xAxisName'
        )
        seriesLineB.forEach(el => {
          el.type = 'line'
          el.yAxisIndex = 1
          el.stack = null
        })
      }
      data.chartF.data = [...seriesBarB, ...seriesLineB]
      data.chartF.title = `${params.manuFacturer}${params.pointerType === '1' ? '季度' : params.pointerType === '2' ? '月累' : '月度'}细分市场销量趋势`
    })
    .finally(() => {
      data.chartE.loading = false
      data.chartF.loading = false
    })

  // 第四个图表
  commVehicleAreaSales(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      const { areaTrendList: barA, areaLineList: lineA } = resData
      barA.forEach(v => {
        v.name = v.province
        v.value = v.sales
        v.tooltipValue = `${v.sales}辆`
      })
      lineA.forEach(v => {
        v.name = v.province
        v.value = v.prop
        v.tooltipValue = `${v.prop}%`
      })
      const series = []
      // 年数据
      const years = [...new Set(barA.map(v => v.year))].sort((a, b) => a - b)
      let sortProvince = [] // 按销量排序的省份
      years.forEach(ele => {
        const data = barA.filter(v => v.year === ele).sort((a, b) => a.top - b.top)
        if (data.length > sortProvince.length) {
          // 最大值
          sortProvince = data.map(v => v.province)
        }
        series.push({ name: ele + '年', data, stack: null, barGap: '0' })
      })
      // 占比折线数据添加
      const lineData = []
      sortProvince.forEach(el => {
        const item = lineA.find(v => v.name === el) || { name: el, value: 0 }
        lineData.push(item)
      })
      if (lineData && lineData.length > 0) {
        series.push({
          name: `${lineA && lineA[0] && lineA[0].year ? lineA[0].year + '年' : ''}玉柴占比`,
          type: 'line',
          yAxisIndex: 1,
          data: lineData
        })
      }
      data.chartG.data = series
      data.chartG.title = `${params.manuFacturer}区域销量`
    })
    .finally(() => {
      data.chartG.loading = false
    })
}

function removeYuChai(series) {
  // 当同时出现 玉柴 和 玉柴占比 的时候删除玉柴
  if (!Array.isArray(series)) return series
  const hasYuChai = series.some(item => item.seriesName === '玉柴')
  const hasYuChaiZhanBi = series.some(item => item.seriesName === '玉柴占比')
  if (hasYuChai && hasYuChaiZhanBi) {
    return series.filter(item => item.seriesName !== '玉柴')
  }
  return series
}

// const TooltipSalesProportionYoYComponent = (propos) => {
//   // 总计销量
//   const barParams = propos.params.filter(e => e.seriesType === 'bar')
//   const totalSale = barParams.reduce((sum, x) => sum + (x?.data?.sales ? x?.data?.sales : 0), 0)
//   const lineParams = propos.params.filter(e => e.seriesType === 'line')[0].value
//   // 删除 同比
//   // 过滤掉 seriesType 为 'line' 的项（即同比）
//   const filteredParams = propos.params.filter(item => item.seriesType !== 'line')
//   filteredParams.sort(a => (a.seriesType === 'line' ? -1 : 1))
//   return (
//     <Tpis {...propos} params={filteredParams}>
//       {{
//         item: ({ item }) => {
//           return (
//             <>
//               <span>
//                销量:{item.data.sales},占比:{item.data.proportion},同比:{item.data.yoy}
//               </span>
//             </>
//           )
//         }
//       }}
//     </Tpis>
//   )
// }

/**
 * 图表2悬浮提示要计算总计有总量和同比
 */
function formatterChartB(params) {
  if (!params) return
  if (params.length > 0 && params.every(el => el.value === '')) return
  // params.reverse()
  let itemsHtml = ''
  for (let i = 0; i < params.length; i++) {
    if (params[i].seriesName !== '总计') {
      if (
        params[i].value === '' ||
        params[i].value === null ||
        params[i].value === undefined ||
        params[i].value === 0 ||
        params[i].seriesName.includes('同比')
      )
        continue
      itemsHtml += `<div class="charts-tooltip-item">
        <span>${params[i].marker}${params[i].seriesName}&nbsp;&nbsp;</span>
       <span> ${params[i].data.tooltipValue ?? params[i].value}</span>
      </div>`
    }
  }
  // 总计销量
  const barParams = params.filter(e => e.seriesType === 'bar')
  const totalSale = barParams.reduce((sum, x) => sum + (x?.data?.sales ? x?.data?.sales : 0), 0)
  // 总计同比
  const lineParams = params.filter(e => e.seriesType === 'line')
  // console.log('lineParams', lineParams)
  let totalSame = ''
  if (lineParams && lineParams.length > 0) {
    totalSame = lineParams[0]?.value ? lineParams[0]?.value : ''
  }
  // const totalSame = barParams.reduce(
  //   (sum, x) => sum + (x?.data?.propChange_px ? x.data.propChange_px : 0),
  //   0
  // )
  const totalHtml =
    totalSale !== 0 && totalSame !== 0
      ? `<div class="charts-tooltip-item">
          <span>总计&nbsp;&nbsp;</span>
          <span>销量:${numberFormat(totalSale, 0)}辆;同比:${numberFormat(totalSame, 1)}%</span>
        </div>`
      : ''
  let tooltipHtml = `
      <div>
        <div> ${params && params[0] ? params[0].name : ''}</div>
        ${itemsHtml}
        ${totalHtml}
      </div>`
  return tooltipHtml
}
</script>
<style lang="scss" scoped>
.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}

:deep(.el-col) {
  margin-bottom: 0px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
