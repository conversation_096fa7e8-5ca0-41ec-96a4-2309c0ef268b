import { reactive } from 'vue'
import { color } from '@/views/components/echarts/config'

export default function () {
  const doubleChartConfig = {
    color,
    // graphic: {
    //   type: 'text',
    //   left: '30%',
    //   style: {
    //     fontSize: 10,
    //     fill: '#44546A'
    //   }
    // },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 0,
      textStyle: {
        //提示框自己的样式
        fontSize: 10,
        // color: '#fff'
        color: '#1D2129'
      },
      axisPointer: {
        label: {
          precision: 2,
          show: true,
          margin: 5,
          backgroundColor: '#0b1f56',
          color: '#fff',
          fontSize: 14
        }
      }
    },
    legend: {
      width: '68%',
      bottom: 0,
      right: '5',
      type: 'scroll',
      orient: 'horizontal',
      itemHeight: '6',
      textStyle: { fontSize: 10 }
    },
    // legend: {
    //   orient: 'vertical',
    //   top: 'middle',
    //   right: 4,
    //   itemHeight: '6',
    //   textStyle: { fontSize: 10 }
    // },
    grid: [
      { left: 40, top: 40, bottom: 80, width: '36%' }, // 第一个图表的位置
      { right: 60, top: 40, bottom: 80, width: '36%' } // 第二个图表的位置
      // { left: 40, top: 40, bottom: 40, width: '22%' }, // 第一个图表的位置
      // { right: 140, top: 40, bottom: 40, width: '36%' } // 第二个图表的位置
    ],
    xAxis: [
      {
        gridIndex: 0,
        type: 'category',
        axisLabel: {
          fontSize: 10,
          color: '#44546A',
          rotate: 45,
          interval: 0
        },
        axisTick: {
          show: true, //显示x轴刻度
          alignWithLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2,
            fontSize: 14
          }
        },
        data: []
      },
      {
        gridIndex: 1,
        type: 'category',
        axisLabel: {
          fontSize: 10,
          color: '#44546A',
          rotate: 45,
          interval: 0
        },
        axisTick: {
          show: true, //显示x轴刻度
          alignWithLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2,
            fontSize: 14
          }
        },
        data: []
      } // 第二个图表的x轴
    ],
    yAxis: [
      {
        max: 100,
        min: 0,
        gridIndex: 0,
        name: '份额：(%)',
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 10,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          color: '#44546A',
          fontSize: 10
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        splitLine: {
          show: false // 隐藏分割线
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2,
            fontSize: 10
          }
        }
      }, // 第一个图表的y轴
      {
        gridIndex: 1,
        name: '单位：(台)',
        type: 'value',
        minInterval: 1,
        nameTextStyle: {
          color: '#44546A',
          fontSize: 10,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          color: '#44546A',
          fontSize: 10
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        splitLine: {
          show: false // 隐藏分割线
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        }
      },
      {
        gridIndex: 1,
        name: '同比：(%)',
        splitNumber: 5,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 10,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          show: true,
          formatter: '{value}%',
          fontSize: 10,
          color: '#44546A'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      } // 第二个图表的y轴
    ],
    series: [
      { type: 'bar', xAxisIndex: 0, yAxisIndex: 0, data: [] }, // 第一个图表的数据
      { type: 'bar', xAxisIndex: 0, yAxisIndex: 0, data: [] }, // 第一个图表的数据2
      { type: 'line', xAxisIndex: 1, yAxisIndex: 1, data: [] } // 第二个图表的数据
    ]
  }
  // 图表1数据配置
  const configChartA = reactive(JSON.parse(JSON.stringify(doubleChartConfig)))
  // 图表2数据配置
  const configChartB = reactive(JSON.parse(JSON.stringify(doubleChartConfig)))
  // 图表3数据配置
  const configChartC = reactive(JSON.parse(JSON.stringify(doubleChartConfig)))
  // 图表4数据配置
  const configChartD = reactive(JSON.parse(JSON.stringify(doubleChartConfig)))

  // 图表5数据配置
  const configChartE = reactive({
    legend: {
      // orient: 'horizontal'
      orient: 'vertical',
      bottom: 100,
      right: 4
    },
    xAxis: [
      {
        axisLabel: {
          rotate: 90,
          fontSize: 14
        }
      }
    ],
    yAxis: [
      {
        name: '单位：(台)',
        minInterval: 1,
      },
      {
        name: '占比：(%)',
        splitNumber: 5,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          show: true,
          formatter: '{value}%',
          fontSize: 12,
          color: '#44546A'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    grid: { bottom: 120, right: 160 },
    series: []
  })
  // 图表6数据配置
  const configChartF = reactive({
    legend: {
      orient: 'vertical',
      bottom: 100,
      right: 4
    },
    xAxis: [
      {
        axisLabel: {
          rotate: 90,
          fontSize: 16
        }
      }
    ],
    yAxis: [
      {
        name: '单位：(台)',
        minInterval: 1,
      },
      {
        name: '同比：(%)',
        splitNumber: 5,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          show: true,
          formatter: '{value}%',
          fontSize: 12,
          color: '#44546A'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [],
    grid: { bottom: 100, right: 160 }
  })

  /**
   * 设置series数据：一维度数组自行分类分X轴Y轴数据(适用于后端返回一维数组，分类xy轴数据都在一个对象里的)
   */
  const setSeriesData = ({ list, xAxisKey, yAxisKey, legendKey }) => {
    const defaultItemJson = { name: '', value: 0 } // 默认的item的所有属性
    // 设置默认的item的所有属性
    if (list && list.length > 0) {
      const item = list[0]
      for (let i in item) {
        defaultItemJson[i] = ''
        if (/^[-+]?(\d+\.?\d*|\.\d+)([eE][-+]?\d+)?$/.test(item[i]))
          defaultItemJson[i] = typeof item[i] === 'string' ? '0' : 0
      }
    }
    // 设置series分类name 分类数据data
    const series = []
    // 设置x轴name y轴value
    list.forEach(v => {
      v.name = v[xAxisKey]
      v.value = v[yAxisKey]
    })
    // 获取所有的分类名称
    const legendNameArray = [...new Set(list.map(v => v[legendKey]))]
    // 把其他项放到最后
    const legendOtherIndex = list.findIndex(e => e === '其他')
    if (legendOtherIndex !== -1) {
      const item = legendNameArray.splice(legendOtherIndex, 1)[0] // 移除指定位置的元素
      legendNameArray.push(item) // 将元素添加到数组末尾
    }
    // 获取所有的X轴不重复的值
    const xAxisNameArray = [...new Set(list.map(v => v[xAxisKey]))]
    // 遍历分类
    legendNameArray.forEach(ele => {
      // 获取同一分类的所有data数据
      const sameData = list.filter(v => v[legendKey] === ele)
      // 补齐每个分类中data缺少的数据
      const data = []
      xAxisNameArray.forEach(el => {
        const item = sameData.find(e => e[xAxisKey] === el)
        if (item) {
          data.push(item)
        } else {
          const json = { ...defaultItemJson }
          json[legendKey] = ele
          json.name = el
          json[xAxisKey] = el
          json.value = 0
          json[yAxisKey] = 0
          data.push(json)
        }
      })
      series.push({ name: ele, data })
    })
    return series
  }
  return {
    configChartA,
    configChartB,
    configChartC,
    configChartD,
    configChartE,
    configChartF,
    setSeriesData
  }
}
